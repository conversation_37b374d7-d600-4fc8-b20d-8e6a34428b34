{"qax": {"enhancePrompt": {"button": "✨", "tooltip": "Qax 优化提示", "enhancing": "优化中..."}, "memoryWorkspace": {"memories": "记忆", "memoriesTitle": "Qax 代码生成记忆", "currentWorkspace": "当前工作区"}, "settings": {"autocomplete": {"title": "自动补全", "enable": "启用自动补全", "delay": "延迟 (毫秒)", "maxLines": "最大行数", "triggerCharacters": "触发字符", "enableQaxAutocomplete": "启用 QAX 自动补全", "provider": "提供者", "openaiCompatible": "OpenAI 兼容", "fim": "FIM (填充中间内容)", "fimApi": "FIM API", "openaiCompatibleApi": "OpenAI 兼容 API", "fimBaseUrl": "FIM 基础 URL", "apiBaseUrl": "API 基础 URL", "modelId": "模型 ID", "maxTokens": "最大令牌数", "temperature": "温度", "timeout": "超时时间 (毫秒)", "debounce": "防抖时间 (毫秒)", "usePromptCache": "使用提示缓存", "note": "注意", "noteDescription": "QAX 自动补全使用 AI 模型提供智能代码补全。可选择：\nOpenAI 兼容：支持 OpenRouter、OpenAI、Azure OpenAI、本地模型 (Ollama、LM Studio) 和其他 OpenAI 兼容服务。\nFIM (填充中间内容)：专门的 API 格式，同时发送前缀和后缀上下文以获得更准确的补全。\n配置您的提供者设置以启用基于当前上下文的智能代码建议。"}, "general": {"title": "设置", "preferredLanguage": "首选语言", "english": "英语", "simplifiedChinese": "简体中文", "done": "完成", "allowAnonymousReporting": "允许匿名错误和使用情况报告", "allowAnonymousReportingDescription": "通过发送匿名使用数据和错误报告来帮助改进 Cline。不会发送任何代码、提示或个人信息。请查看我们的", "telemetryOverview": "遥测概述", "and": "和", "privacyPolicy": "隐私政策", "forMoreDetails": "了解更多详情。", "languageDescription": "The language that C<PERSON> should use for communication."}, "tabs": {"apiConfiguration": "API 配置", "autocomplete": "自动补全", "autocompleteSettings": "自动补全设置", "qaxAutocompleteSettings": "QAX 自动补全设置", "general": "通用", "generalSettings": "通用设置", "features": "功能", "featureSettings": "功能设置", "browser": "浏览器", "browserSettings": "浏览器设置", "terminal": "终端", "terminalSettings": "终端设置", "debug": "调试", "debugTools": "调试工具", "debugHeader": "调试", "about": "关于", "aboutCline": "关于 Cline", "aboutHeader": "关于"}, "api": {"provider": "API提供者", "planMode": "计划模式", "actMode": "执行模式", "useDifferentModels": "为计划和执行模式使用不同的模型", "useDifferentModelsDescription": "在计划和执行模式之间切换时，将保留之前模式中使用的 API 和模型。这可能很有帮助，例如使用强大的推理模型来为更便宜的编码模型制定计划。"}, "features": {"enableCheckpoints": "启用检查点", "enableCheckpointsDescription": "启用扩展在整个任务过程中保存工作区检查点。底层使用 git，可能不适用于大型工作区。", "enableMcpMarketplace": "启用 MCP 市场", "enableMcpMarketplaceDescription": "启用 MCP 市场标签页以发现和安装 MCP 服务器。", "mcpDisplayMode": "MCP 显示模式", "mcpDisplayModeDescription": "控制 MCP 响应的显示方式：纯文本、带链接/图片的丰富格式或 Markdown 渲染。", "collapseMcpResponses": "折叠 MCP 响应", "collapseMcpResponsesDescription": "设置 MCP 响应面板的默认显示模式", "plainText": "纯文本", "richDisplay": "丰富显示", "markdown": "<PERSON><PERSON>", "openaiReasoningEffort": "OpenAI 推理力度", "low": "低", "medium": "中", "high": "高", "openaiReasoningEffortDescription": "OpenAI 系列模型的推理力度（适用于所有 OpenAI 模型提供商）"}, "browser": {"disableBrowserToolUsage": "禁用浏览器工具使用", "disableBrowserToolUsageDescription": "阻止 Cline 使用浏览器操作（例如启动、点击、输入）。", "viewportSize": "视口大小", "viewportSizeDescription": "设置浏览器视口的大小以进行截图和交互。", "useRemoteBrowserConnection": "使用远程浏览器连接", "chromeDescription": "启用 Cline 使用您的 Chrome{isBundled}{hasRemoteBrowserEnabled, select, true { 手动 (--remote-debugging-port=9222) 或使用下面的按钮。输入主机地址或留空以自动发现。} other {。}}", "notDetected": "（在您的机器上未检测到）", "chromeExecutablePath": "Chrome 可执行文件路径（可选）", "chromeExecutablePathDescription": "留空以自动检测。", "launchingBrowser": "正在启动浏览器...", "launchBrowserWithDebugMode": "以调试模式启动浏览器"}, "terminal": {"defaultTerminalProfile": "默认终端配置文件", "defaultTerminalProfileDescription": "选择 Cline 将使用的默认终端。'默认'使用您的 VSCode 全局设置。", "shellIntegrationTimeout": "Shell 集成超时（秒）", "enterTimeout": "输入超时时间（秒）", "shellIntegrationTimeoutDescription": "设置 Cline 在执行命令之前等待 shell 集成激活的时间。如果您遇到终端连接超时，请增加此值。", "enableAggressiveTerminalReuse": "启用激进的终端重用", "enableAggressiveTerminalReuseDescription": "启用后，Cline 将重用不在当前工作目录中的现有终端窗口。如果您在终端命令后遇到任务锁定问题，请禁用此功能。", "outputLimit": "终端输出限制", "outputLimitDescription": "执行命令时终端输出中包含的最大行数。超出时，将从中间删除行以节省令牌。"}, "note": "注意", "noteDescription": "QAX 自动补全使用 AI 模型提供智能代码补全。可选择：\nOpenAI 兼容：支持 OpenRouter、OpenAI、Azure OpenAI、本地模型 (Ollama、LM Studio) 和其他 OpenAI 兼容服务。\nFIM (填充中间内容)：专门的 API 格式，同时发送前缀和后缀上下文以获得更准确的补全。\n配置您的提供者设置以启用基于当前上下文的智能代码建议。"}, "todo": {"title": "待办列表", "addTask": "添加任务", "empty": "暂无任务", "completed": "已完成", "addTodoPrompt": "输入待办事项：", "delete": "删除待办", "deleteConfirm": "确定要删除这个待办事项吗？"}, "account": {"title": "QAX 账户", "done": "完成", "loginPrompt": "请登录 QAX 账户以查看用户信息和使用 QAX Codegen 服务。", "loginButton": "登录 QAX 账户", "tokenStatus": "TOKEN 状态", "expirationTime": "过期时间", "tokenExpired": "Token 已过期，请重新登录", "tokenExpiringSoon": "Token 将在 24 小时内过期", "tokenValid": "Token 有效", "logout": "退出登录"}, "mcp": {"title": "MCP 服务器", "done": "完成", "marketplace": "市场", "remoteServers": "远程服务器", "installed": "已安装", "searchPlaceholder": "搜索 MCP...", "filter": "筛选:", "allCategories": "所有分类", "sort": "排序:", "mostInstalls": "最多安装", "newest": "最新", "githubStars": "GitHub 星标", "name": "名称", "noMatchingServers": "未找到匹配的 MCP 服务器", "noServersFound": "市场中未找到 MCP 服务器", "installing": "安装中...", "install": "安装", "requiresApiKey": "需要 API 密钥", "loadingPreview": "正在加载预览", "waitingFor": "等待", "unableToLoadPreview": "无法加载预览", "previewTimeout": "预览请求超时", "networkError": "网络错误加载预览", "clickToOpen": "点击在浏览器中打开", "noTitle": "无标题", "noDescription": "无描述信息", "loadingImage": "正在加载图片", "failedToLoadImage": "加载图片失败", "addRemoteServer": "通过提供名称和 URL 端点添加远程 MCP 服务器。了解更多", "here": "信息。", "serverName": "服务器名称", "serverUrl": "服务器 URL", "serverNameRequired": "服务器名称为必填项", "serverUrlRequired": "服务器 URL 为必填项", "invalidUrl": "URL 格式无效", "adding": "添加中...", "addServer": "添加服务器", "connecting": "正在连接服务器... 这可能需要几秒钟。", "editConfiguration": "编辑配置", "response": "响应", "responseError": "响应 (错误)", "errorParsingResponse": "解析响应时出错:"}, "telemetry": {"closeBanner": "关闭横幅", "helpImproveCline": "通过发送匿名使用数据和错误报告来帮助改进 Cline。", "experimentalFeatures": "可能启用了实验性功能。", "dataCollection": "不会发送任何代码、提示或个人信息。", "turnOffSetting": "您可以随时在", "settings": "设置"}, "chat": {"creditLimit": {"outOfCredit": "您的积分已用完！", "currentBalance": "当前余额", "totalSpent": "总支出", "totalPromotions": "总促销", "buyCredits": "购买积分", "retryRequest": "重试请求"}, "quote": {"selection": "引用选择", "selectionInReply": "在回复中引用选择"}}, "clineRules": {"manageRulesAndWorkflows": "管理 Cline 规则和工作流", "clineRules": "Cline 规则", "rules": "规则", "workflows": "工作流", "rulesDescription": "Cline 规则是包含 Cline 将始终遵循的指令和上下文的 Markdown 文件。它们会自动包含在每个任务中。在", "workflowsDescription1": "工作流是可以通过在聊天中输入", "workflowName": "/workflow-name", "workflowsDescription2": "来触发的斜杠命令。它们可用于自动化常见任务或快速访问常用提示。在", "docs": "文档", "globalRules": "全局规则", "workspaceRules": "工作区规则", "globalWorkflows": "全局工作流", "workspaceWorkflows": "工作区工作流"}, "browser": {"connectionInfo": "浏览器连接信息", "connection": "连接", "status": "状态", "connected": "已连接", "disconnected": "已断开", "type": "类型", "remote": "远程", "local": "本地", "remoteHost": "远程主机"}, "history": {"title": "历史记录", "done": "完成", "searchPlaceholder": "搜索任务...", "newest": "最新", "oldest": "最旧", "mostExpensive": "最昂贵", "mostTokens": "最多令牌", "mostRelevant": "最相关", "workspace": "工作区", "favorites": "收藏", "selectAll": "全选", "selectNone": "取消全选", "export": "导出", "deleteSelected": "删除选中", "selected": "选中", "deleteAll": "删除全部"}, "welcome": {"title": "欢迎使用 Cline", "description1": "Cline 是一个由", "description2": "驱动的 AI 编码助手——世界上最先进的代码 AI。Cline 可以理解您的整个代码库并执行复杂的任务，这些任务如果由您自己完成可能需要数小时或数天时间。", "description3": "目前处于测试阶段", "description4": "Cline 可以理解您的整个代码库并执行复杂的任务，这些任务如果由您自己完成可能需要数小时或数天时间。", "signup": "注册 Cline 账户以开始使用 AI 编码助手。", "getStarted": "开始使用", "useApiKey": "改用 API 密钥", "letsGo": "开始吧！"}, "about": {"questionsOrFeedback": "如果您有任何问题或反馈，请随时在以下地址提交问题："}, "debug": {"resetWorkspaceState": "重置工作区状态", "resetGlobalState": "重置全局状态", "resetStateDescription": "这将重置扩展中的所有全局状态和密钥存储。"}}}