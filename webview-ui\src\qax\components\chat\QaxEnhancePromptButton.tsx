import Tooltip from "@/components/common/Tooltip"
import { QaxUtilsServiceClient } from "@/services/grpc-client"
import { StringRequest } from "@shared/proto/cline/common"
import { VSCodeButton } from "@vscode/webview-ui-toolkit/react"
import React, { useState, useRef, useCallback } from "react"
import { useTranslation } from "@/utils/i18n"

interface QaxEnhancePromptButtonProps {
	inputValue: string
	setInputValue: (value: string) => void
	sendingDisabled: boolean
}

const QaxEnhancePromptButton: React.FC<QaxEnhancePromptButtonProps> = ({ inputValue, setInputValue, sendingDisabled }) => {
	const [isEnhancing, setIsEnhancing] = useState(false)
	const abortControllerRef = useRef<AbortController | null>(null)
	const { t } = useTranslation()

	const handleEnhancePrompt = useCallback(async () => {
		if (!inputValue.trim() || sendingDisabled || isEnhancing) {
			return
		}

		console.log("[QaxEnhancePromptButton] Starting enhancement for:", inputValue.substring(0, 100) + "...")
		setIsEnhancing(true)

		// Create abort controller for cancellation
		abortControllerRef.current = new AbortController()

		try {
			console.log("[QaxEnhancePromptButton] Calling QaxUtilsServiceClient.enhancePrompt...")
			// Get the full response first, don't clear input yet
			const response = await QaxUtilsServiceClient.enhancePrompt(StringRequest.create({ value: inputValue }))
			console.log("[QaxEnhancePromptButton] Received response:", response)

			if (response.value && !abortControllerRef.current.signal.aborted) {
				console.log("[QaxEnhancePromptButton] Starting streaming output...")

				// Now clear the input and start streaming the new content
				setInputValue("")

				// Stream the response character by character
				const enhancedText = response.value
				let currentText = ""

				for (let i = 0; i < enhancedText.length; i++) {
					if (abortControllerRef.current.signal.aborted) {
						break
					}

					currentText += enhancedText[i]
					setInputValue(currentText)

					// Auto-scroll to bottom during streaming to keep up with new content
					setTimeout(() => {
						const textArea = document.querySelector('[data-testid="chat-input"]') as HTMLTextAreaElement
						if (textArea) {
							textArea.scrollTop = textArea.scrollHeight
						}
					}, 0)

					// Add a small delay to simulate streaming
					await new Promise((resolve) => setTimeout(resolve, 20))
				}

				console.log("[QaxEnhancePromptButton] Streaming completed")
			} else if (!response.value) {
				console.warn("[QaxEnhancePromptButton] Enhanced prompt not available")
				// Keep original input if enhancement fails
			}
		} catch (error) {
			console.error("[QaxEnhancePromptButton] Failed to enhance prompt:", error)
			// Keep original input on error - don't change anything
		} finally {
			setIsEnhancing(false)
			abortControllerRef.current = null
		}
	}, [inputValue, setInputValue, sendingDisabled, isEnhancing])

	// Don't render the button if there's no input content
	if (!inputValue.trim()) {
		return null
	}

	const isDisabled = sendingDisabled || isEnhancing

	return (
		<Tooltip tipText={t("qax.enhancePrompt.tooltip")}>
			<VSCodeButton
				appearance="icon"
				aria-label={t("qax.enhancePrompt.tooltip")}
				disabled={isDisabled}
				onClick={handleEnhancePrompt}
				style={{ padding: "0px 0px", height: "20px" }}>
				<div className="flex items-center gap-1 text-xs whitespace-nowrap min-w-0 w-full">
					{isEnhancing ? (
						<span
							className="codicon codicon-loading codicon-modifier-spin flex items-center"
							style={{ fontSize: "12.5px", marginBottom: 1 }}
						/>
					) : (
						<span style={{ fontSize: "14px", marginBottom: 1 }}>✨</span>
					)}
				</div>
			</VSCodeButton>
		</Tooltip>
	)
}

export default QaxEnhancePromptButton
