import { useExtensionState } from "@/context/ExtensionStateContext"
import { ModelsServiceClient } from "@/services/grpc-client"
import { StringRequest } from "@shared/proto/cline/common"
import { VSCodeDropdown, VSCodeOption } from "@vscode/webview-ui-toolkit/react"
import { useCallback, useEffect, useState } from "react"
import { useInterval } from "react-use"
import styled from "styled-components"
import { OPENROUTER_MODEL_PICKER_Z_INDEX } from "@/components/settings/OpenRouterModelPicker"
import { normalizeApiConfiguration } from "@/components/settings/utils/providerUtils"
import { useApiConfigurationHandlers } from "@/components/settings/utils/useApiConfigurationHandlers"
import { Mode } from "@shared/ChatSettings"
import { QaxCodegenProvider } from "@/qax/components/settings/providers/QaxCodegenProvider"
import { QaxProvider } from "@/qax/components/settings/providers/QaxProvider"

interface ApiOptionsProps {
	showModelOptions: boolean
	apiErrorMessage?: string
	modelIdErrorMessage?: string
	isPopup?: boolean
	currentMode: Mode
}

// This is necessary to ensure dropdown opens downward, important for when this is used in popup
export const DROPDOWN_Z_INDEX = OPENROUTER_MODEL_PICKER_Z_INDEX + 2 // Higher than the OpenRouterModelPicker's and ModelSelectorTooltip's z-index

export const DropdownContainer = styled.div<{ zIndex?: number }>`
	position: relative;
	z-index: ${(props) => props.zIndex || DROPDOWN_Z_INDEX};

	// Force dropdowns to open downward
	& vscode-dropdown::part(listbox) {
		position: absolute !important;
		top: 100% !important;
		bottom: auto !important;
	}
`

declare module "vscode" {
	interface LanguageModelChatSelector {
		vendor?: string
		family?: string
		version?: string
		id?: string
	}
}

/**
 * QAX 专用的 API Options 组件
 * 只包含 qax 和 qax-codegen 两个 provider 选项
 * 在 QAX 构建时会替换原始的 ApiOptions 组件
 */
const ApiOptions = ({ showModelOptions, apiErrorMessage, modelIdErrorMessage, isPopup, currentMode }: ApiOptionsProps) => {
	const { apiConfiguration } = useExtensionState()
	const { handleModeFieldChange } = useApiConfigurationHandlers()
	const [isRefreshing, setIsRefreshing] = useState(false)

	const { selectedProvider } = normalizeApiConfiguration(apiConfiguration, currentMode)

	// Refresh models periodically
	const refreshModels = useCallback(async () => {
		if (isRefreshing) return
		setIsRefreshing(true)
		try {
			await ModelsServiceClient.refreshOpenRouterModels(StringRequest.create({ value: "" }))
		} catch (error) {
			console.error("Failed to refresh models:", error)
		} finally {
			setIsRefreshing(false)
		}
	}, [isRefreshing])

	useInterval(refreshModels, 1000 * 60 * 5) // 5 minutes

	useEffect(() => {
		refreshModels()
	}, [refreshModels])

	return (
		<div>
			<DropdownContainer>
				<VSCodeDropdown
					value={selectedProvider}
					onChange={(e: any) => {
						handleModeFieldChange(
							{ plan: "planModeApiProvider", act: "actModeApiProvider" },
							e.target.value,
							currentMode,
						)
					}}
					style={{
						minWidth: 130,
						position: "relative",
					}}>
					<VSCodeOption value="qax">QAX</VSCodeOption>
					<VSCodeOption value="qax-codegen">QAX Codegen</VSCodeOption>
				</VSCodeDropdown>
			</DropdownContainer>

			{apiConfiguration && selectedProvider === "qax" && (
				<QaxProvider showModelOptions={showModelOptions} isPopup={isPopup} currentMode={currentMode} />
			)}

			{apiConfiguration && selectedProvider === "qax-codegen" && (
				<QaxCodegenProvider showModelOptions={showModelOptions} isPopup={isPopup} currentMode={currentMode} />
			)}

			{apiErrorMessage && (
				<p
					style={{
						margin: "-10px 0 4px 0",
						fontSize: 12,
						color: "var(--vscode-errorForeground)",
					}}>
					{apiErrorMessage}
				</p>
			)}
			{modelIdErrorMessage && (
				<p
					style={{
						margin: "-10px 0 4px 0",
						fontSize: 12,
						color: "var(--vscode-errorForeground)",
					}}>
					{modelIdErrorMessage}
				</p>
			)}
		</div>
	)
}

export default ApiOptions
