import { useEffect, useRef } from "react"
import { vscode } from "../utils/vscode"

/**
 * Hook to sync send button state with autocomplete manager
 * This ensures autocomplete is disabled when send button is disabled
 */
export function useAutocompleteSyncState(sendingDisabled: boolean) {
	const previousStateRef = useRef<boolean>(sendingDisabled)

	useEffect(() => {
		// Only send update if state actually changed
		if (previousStateRef.current !== sendingDisabled) {
			previousStateRef.current = sendingDisabled

			// Send state update to backend
			vscode.postMessage({
				type: "autocomplete_state_sync",
				autocomplete_state_sync: {
					sendingDisabled,
					timestamp: Date.now(),
				},
			})

			console.log(`🔄 Autocomplete state sync: sendingDisabled=${sendingDisabled}`)
		}
	}, [sendingDisabled])
}
