import { useExtensionState } from "@/context/ExtensionStateContext"
import { VSCodeCheckbox, VSCodeTextField, VSCodeDropdown, VSCodeOption } from "@vscode/webview-ui-toolkit/react"
import { useState } from "react"
import Section from "@components/settings/Section"
import { Api<PERSON>eyField } from "@components/settings/common/ApiKeyField"
import { ErrorMessage } from "@components/settings/common/ErrorMessage"
import { DropdownContainer } from "@components/settings/common/ModelSelector"
import { SmartTextField } from "@components/settings/common/SmartTextField"
import { QaxAutocompleteSettings, QaxAutocompleteProvider } from "@shared/QaxAutocompleteSettings"
import { updateSetting } from "@components/settings/utils/settingsHandlers"
import { convertQaxAutocompleteSettingsToProtoAutocompleteSettings } from "@shared/proto-conversions/state/qax-autocomplete-settings-conversion"
import { useTranslation } from "@/utils/i18n"

interface AutocompleteSettingsSectionProps {
	renderSectionHeader: (tabId: string) => JSX.Element | null
}

export default function AutocompleteSettingsSection({ renderSectionHeader }: AutocompleteSettingsSectionProps) {
	const { autocompleteSettings } = useExtensionState()
	const [errors, setErrors] = useState<string[]>([])
	const { t } = useTranslation()

	// 立即保存单个字段的函数
	const saveField = (field: string, value: any) => {
		let newSettings: QaxAutocompleteSettings

		if (field.startsWith("fim.")) {
			const fimField = field.split(".")[1] as keyof NonNullable<typeof autocompleteSettings.fim>
			newSettings = {
				...autocompleteSettings,
				fim: {
					...autocompleteSettings.fim,
					[fimField]: value,
				},
			}
		} else {
			newSettings = {
				...autocompleteSettings,
				[field]: value,
			}
		}

		// 立即保存到后端
		const protoSettings = convertQaxAutocompleteSettingsToProtoAutocompleteSettings(newSettings)
		updateSetting("autocompleteSettings", protoSettings)
	}

	// Provider切换的特殊处理 - 需要设置默认值
	const handleProviderChange = (newProvider: string) => {
		let newSettings = { ...autocompleteSettings, provider: newProvider as QaxAutocompleteProvider }

		// 只在字段为空时设置默认值
		if (newProvider === "fim") {
			if (!newSettings.fim?.baseUrl) {
				newSettings.fim = {
					...newSettings.fim,
					baseUrl: "http://kubemlsvc.qianxin-inc.cn/svc-d70rsdpypzxs/v1/completions",
				}
			}
			if (!newSettings.fim?.modelId) {
				newSettings.fim = {
					...newSettings.fim,
					modelId: "Qwen7b",
				}
			}
		} else if (newProvider === "openai") {
			if (!newSettings.apiBaseUrl) {
				newSettings.apiBaseUrl = "https://api.openrouter.ai/api/v1"
			}
			if (!newSettings.modelId) {
				newSettings.modelId = "google/gemini-2.5-flash-preview-05-20"
			}
		}

		// 立即保存到后端
		const protoSettings = convertQaxAutocompleteSettingsToProtoAutocompleteSettings(newSettings)
		updateSetting("autocompleteSettings", protoSettings)
	}

	// Checkbox处理 - 立即保存
	const handleCheckboxChange = (field: string) => (event: any) => {
		const checked = event.target?.checked ?? event.detail?.checked ?? event
		saveField(field, checked)
	}

	// 数字字段处理 - 立即保存
	const handleNumberChange = (field: string) => (event: any) => {
		const value = parseInt((event.target as HTMLInputElement)?.value) || 0
		saveField(field, value)
	}

	// 浮点数字段处理 - 立即保存
	const handleFloatChange = (field: string) => (event: any) => {
		const value = parseFloat((event.target as HTMLInputElement)?.value) || 0
		saveField(field, value)
	}

	return (
		<div>
			{renderSectionHeader("autocomplete")}
			<Section>
				{errors.length > 0 && (
					<div style={{ marginBottom: "16px" }}>
						{errors.map((error, index) => (
							<ErrorMessage key={index} message={error} />
						))}
					</div>
				)}

				<div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
					<VSCodeCheckbox checked={autocompleteSettings.enabled} onChange={handleCheckboxChange("enabled")}>
						{t("qax.settings.autocomplete.enableQaxAutocomplete")}
					</VSCodeCheckbox>

					<DropdownContainer className="dropdown-container">
						<label htmlFor="autocomplete-provider">
							<span style={{ fontWeight: 500 }}>{t("qax.settings.autocomplete.provider")}</span>
						</label>
						<VSCodeDropdown
							id="autocomplete-provider"
							value={autocompleteSettings.provider || "openai"}
							onChange={(e: any) => handleProviderChange(e.target?.value || e.detail?.value)}
							style={{ width: "100%" }}>
							<VSCodeOption value="openai">{t("qax.settings.autocomplete.openaiCompatible")}</VSCodeOption>
							<VSCodeOption value="fim">{t("qax.settings.autocomplete.fim")}</VSCodeOption>
						</VSCodeDropdown>
					</DropdownContainer>

					{autocompleteSettings.provider === "fim" ? (
						<>
							<ApiKeyField
								initialValue={autocompleteSettings.fim?.apiKey || ""}
								onChange={(value: string) => saveField("fim.apiKey", value)}
								providerName={t("qax.settings.autocomplete.fimApi")}
								placeholder="Enter your FIM API key..."
							/>

							<SmartTextField
								value={autocompleteSettings.fim?.baseUrl || ""}
								onChange={(value: string) => saveField("fim.baseUrl", value)}
								placeholder={"http://kubemlsvc.qianxin-inc.cn/svc-d70rsdpypzxs/v1/completions"}>
								<span style={{ fontWeight: 500 }}>{t("qax.settings.autocomplete.fimBaseUrl")}</span>
							</SmartTextField>
						</>
					) : (
						<>
							<ApiKeyField
								initialValue={autocompleteSettings.apiKey || ""}
								onChange={(value: string) => saveField("apiKey", value)}
								providerName={t("qax.settings.autocomplete.openaiCompatibleApi")}
								placeholder="Enter your API key..."
							/>

							<SmartTextField
								value={autocompleteSettings.apiBaseUrl || ""}
								onChange={(value: string) => saveField("apiBaseUrl", value)}
								placeholder={"https://aip.b.qianxin-inc.cn/v2"}>
								<span style={{ fontWeight: 500 }}>{t("qax.settings.autocomplete.apiBaseUrl")}</span>
							</SmartTextField>
						</>
					)}

					<SmartTextField
						value={
							autocompleteSettings.provider === "fim"
								? autocompleteSettings.fim?.modelId || ""
								: autocompleteSettings.modelId || ""
						}
						onChange={(value: string) =>
							saveField(autocompleteSettings.provider === "fim" ? "fim.modelId" : "modelId", value)
						}
						style={{ width: "100%" }}
						placeholder={
							autocompleteSettings.provider === "fim" ? "Qwen7b" : "google/gemini-2.5-flash-preview-05-20"
						}>
						<span style={{ fontWeight: 500 }}>{t("qax.settings.autocomplete.modelId")}</span>
					</SmartTextField>

					{/* 数字选项方格布局 */}
					<div
						style={{
							display: "grid",
							gridTemplateColumns: "repeat(auto-fit, minmax(120px, 1fr))",
							gap: "12px",
							marginTop: "8px",
							maxWidth: "100%",
						}}>
						{/* Max Tokens 方格 */}
						<div
							style={{
								display: "flex",
								flexDirection: "column",
								alignItems: "center",
								justifyContent: "center",
								padding: "12px 6px",
								backgroundColor: "var(--vscode-editor-background)",
								border: "1px solid var(--vscode-input-border)",
								borderRadius: "6px",
								minHeight: "80px",
								boxSizing: "border-box",
							}}>
							<span
								style={{
									fontWeight: 500,
									marginBottom: "6px",
									fontSize: "11px",
									textAlign: "center",
									whiteSpace: "nowrap",
								}}>
								{t("qax.settings.autocomplete.maxTokens")}
							</span>
							<VSCodeTextField
								value={autocompleteSettings.maxTokens?.toString() || ""}
								style={{ width: "calc(100% - 8px)", maxWidth: "80px", textAlign: "center" }}
								onInput={handleNumberChange("maxTokens")}
								placeholder="1000"></VSCodeTextField>
						</div>

						{/* Temperature 方格 */}
						<div
							style={{
								display: "flex",
								flexDirection: "column",
								alignItems: "center",
								justifyContent: "center",
								padding: "12px 6px",
								backgroundColor: "var(--vscode-editor-background)",
								border: "1px solid var(--vscode-input-border)",
								borderRadius: "6px",
								minHeight: "80px",
								boxSizing: "border-box",
							}}>
							<span
								style={{
									fontWeight: 500,
									marginBottom: "6px",
									fontSize: "11px",
									textAlign: "center",
									whiteSpace: "nowrap",
								}}>
								{t("qax.settings.autocomplete.temperature")}
							</span>
							<VSCodeTextField
								value={autocompleteSettings.temperature?.toString() || ""}
								style={{ width: "calc(100% - 8px)", maxWidth: "80px", textAlign: "center" }}
								onInput={handleFloatChange("temperature")}
								placeholder="0.1"></VSCodeTextField>
						</div>

						{/* Request Timeout 方格 */}
						<div
							style={{
								display: "flex",
								flexDirection: "column",
								alignItems: "center",
								justifyContent: "center",
								padding: "12px 6px",
								backgroundColor: "var(--vscode-editor-background)",
								border: "1px solid var(--vscode-input-border)",
								borderRadius: "6px",
								minHeight: "80px",
								boxSizing: "border-box",
							}}>
							<span
								style={{
									fontWeight: 500,
									marginBottom: "6px",
									fontSize: "11px",
									textAlign: "center",
									whiteSpace: "nowrap",
								}}>
								{t("qax.settings.autocomplete.timeout")}
							</span>
							<VSCodeTextField
								value={autocompleteSettings.requestTimeoutMs?.toString() || ""}
								style={{ width: "calc(100% - 8px)", maxWidth: "80px", textAlign: "center" }}
								onInput={handleNumberChange("requestTimeoutMs")}
								placeholder="30000"></VSCodeTextField>
						</div>

						{/* Debounce Time 方格 */}
						<div
							style={{
								display: "flex",
								flexDirection: "column",
								alignItems: "center",
								justifyContent: "center",
								padding: "12px 6px",
								backgroundColor: "var(--vscode-editor-background)",
								border: "1px solid var(--vscode-input-border)",
								borderRadius: "6px",
								minHeight: "80px",
								boxSizing: "border-box",
							}}>
							<span
								style={{
									fontWeight: 500,
									marginBottom: "6px",
									fontSize: "11px",
									textAlign: "center",
									whiteSpace: "nowrap",
								}}>
								{t("qax.settings.autocomplete.debounce")}
							</span>
							<VSCodeTextField
								value={autocompleteSettings.debounceMs?.toString() || ""}
								style={{ width: "calc(100% - 8px)", maxWidth: "80px", textAlign: "center" }}
								onInput={handleNumberChange("debounceMs")}
								placeholder="300"></VSCodeTextField>
						</div>
					</div>

					<VSCodeCheckbox
						checked={autocompleteSettings.usePromptCache || false}
						onChange={handleCheckboxChange("usePromptCache")}>
						{t("qax.settings.autocomplete.usePromptCache")}
					</VSCodeCheckbox>
				</div>

				<div
					style={{
						marginTop: "24px",
						padding: "12px",
						backgroundColor: "var(--vscode-textBlockQuote-background)",
						border: "1px solid var(--vscode-textBlockQuote-border)",
						borderRadius: "4px",
					}}>
					<p
						style={{
							margin: 0,
							fontSize: "12px",
							color: "var(--vscode-descriptionForeground)",
						}}>
						<strong>Note:</strong> QAX Autocomplete provides intelligent code completion using AI models. Choose
						between:
						<br />
						<strong>OpenAI Compatible:</strong> Works with OpenRouter, OpenAI, Azure OpenAI, local models (Ollama, LM
						Studio), and other OpenAI-compatible services.
						<br />
						<strong>FIM (Fill in the Middle):</strong> Specialized API format that sends both prefix and suffix
						context for more accurate completions.
						<br />
						Configure your provider settings to enable intelligent code suggestions based on your current context.
					</p>
				</div>
			</Section>
		</div>
	)
}
