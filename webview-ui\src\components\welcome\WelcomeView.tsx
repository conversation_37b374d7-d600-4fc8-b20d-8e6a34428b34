import { VSCodeButton, VSCodeLink } from "@vscode/webview-ui-toolkit/react"
import { useEffect, useState, memo } from "react"
import { useExtensionState } from "@/context/ExtensionStateContext"
import { validateApiConfiguration } from "@/utils/validate"
import ApiOptions from "@/components/settings/ApiOptions"
import ClineLogoWhite from "@/assets/ClineLogoWhite"
import { AccountServiceClient, StateServiceClient } from "@/services/grpc-client"
import { EmptyRequest, BooleanRequest } from "@shared/proto/cline/common"
import { useTranslation } from "@/utils/i18n"

const WelcomeView = memo(() => {
	const { apiConfiguration, chatSettings } = useExtensionState()
	const [apiErrorMessage, setApiErrorMessage] = useState<string | undefined>(undefined)
	const [showApiOptions, setShowApiOptions] = useState(false)
	const { t, isLoaded } = useTranslation()

	const disableLetsGoButton = apiErrorMessage != null

	// 如果翻译资源还没有加载完成，显示加载状态
	if (!isLoaded) {
		return (
			<div className="fixed inset-0 p-0 flex flex-col items-center justify-center">
				<div className="text-[var(--vscode-foreground)]">Loading...</div>
			</div>
		)
	}

	const handleLogin = () => {
		AccountServiceClient.accountLoginClicked(EmptyRequest.create()).catch((err) =>
			console.error("Failed to get login URL:", err),
		)
	}

	const handleSubmit = async () => {
		try {
			await StateServiceClient.setWelcomeViewCompleted(BooleanRequest.create({ value: true }))
		} catch (error) {
			console.error("Failed to update API configuration or complete welcome view:", error)
		}
	}

	useEffect(() => {
		setApiErrorMessage(validateApiConfiguration(chatSettings.mode, apiConfiguration))
	}, [apiConfiguration, chatSettings.mode])

	return (
		<div className="fixed inset-0 p-0 flex flex-col">
			<div className="h-full px-5 overflow-auto">
				<h2>{t("welcome.title")}</h2>
				<div className="flex justify-center my-5">
					<ClineLogoWhite className="size-16" />
				</div>
				<p>
					{t("welcome.description1")}{" "}
					<VSCodeLink href="https://www.anthropic.com/claude/sonnet" className="inline">
						Claude 4 Sonnet's
					</VSCodeLink>
					{t("welcome.description2")} <i>({t("welcome.description3")})</i>. {t("welcome.description4")}
				</p>

				<p className="text-[var(--vscode-descriptionForeground)]">
					{t("welcome.signup")}
				</p>

				<VSCodeButton appearance="primary" onClick={handleLogin} className="w-full mt-1">
					{t("welcome.getStarted")}
				</VSCodeButton>

				{!showApiOptions && (
					<VSCodeButton
						appearance="secondary"
						onClick={() => setShowApiOptions(!showApiOptions)}
						className="mt-2.5 w-full">
						{t("welcome.useApiKey")}
					</VSCodeButton>
				)}

				<div className="mt-4.5">
					{showApiOptions && (
						<div>
							<ApiOptions showModelOptions={false} currentMode={chatSettings.mode} />
							<VSCodeButton onClick={handleSubmit} disabled={disableLetsGoButton} className="mt-0.75">
								{t("welcome.letsGo")}
							</VSCodeButton>
						</div>
					)}
				</div>
			</div>
		</div>
	)
})

export default WelcomeView
