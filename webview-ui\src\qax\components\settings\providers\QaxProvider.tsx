import { openAiModelInfoSaneDefaults } from "@shared/api"
import { OpenAiModelsRequest } from "@shared/proto/cline/models"
import { QaxUtilsServiceClient } from "@/services/grpc-client"
import { getAsVar, VSC_DESCRIPTION_FOREGROUND } from "@/utils/vscStyles"
import { VSCodeCheckbox, VSCodeDropdown, VSCodeOption } from "@vscode/webview-ui-toolkit/react"
import { useCallback, useEffect, useRef, useState } from "react"
import { DebouncedTextField } from "@/components/settings/common/DebouncedTextField"
import { ModelInfoView } from "@/components/settings/common/ModelInfoView"
import { ApiKeyField } from "@/components/settings/common/ApiKeyField"
import { BaseUrlField } from "@/components/settings/common/BaseUrlField"
import { normalizeApiConfiguration } from "@/components/settings/utils/providerUtils"
import { useExtensionState } from "@/context/ExtensionStateContext"
import { useApiConfigurationHandlers } from "@/components/settings/utils/useApiConfigurationHandlers"
import { getQaxAIPlatformBaseUrl } from "@shared/qax"
import { Mode } from "@shared/ChatSettings"

/**
 * Props for the QaxProvider component
 */
interface QaxProviderProps {
	showModelOptions: boolean
	isPopup?: boolean
	currentMode: Mode
}

/**
 * The QAX provider configuration component
 */
export const QaxProvider = ({ showModelOptions, isPopup, currentMode }: QaxProviderProps) => {
	const { apiConfiguration } = useExtensionState()
	const { handleFieldChange } = useApiConfigurationHandlers()

	const [modelConfigurationSelected, setModelConfigurationSelected] = useState(false)
	const [availableModels, setAvailableModels] = useState<string[]>([])
	const [isLoadingModels, setIsLoadingModels] = useState(false)

	// Get the normalized configuration
	const { selectedModelId, selectedModelInfo } = normalizeApiConfiguration(apiConfiguration, currentMode)

	// 根据当前模式获取对应的模型ID
	const getCurrentModelId = () => {
		return currentMode === "plan" ? apiConfiguration?.planModeQaxModelId || "" : apiConfiguration?.actModeQaxModelId || ""
	}

	// 根据当前模式获取对应的模型信息
	const getCurrentModelInfo = () => {
		return currentMode === "plan" ? apiConfiguration?.planModeQaxModelInfo : apiConfiguration?.actModeQaxModelInfo
	}

	// 根据当前模式更新模型ID
	const updateModelId = (modelId: string) => {
		if (currentMode === "plan") {
			handleFieldChange("planModeQaxModelId", modelId)
		} else {
			handleFieldChange("actModeQaxModelId", modelId)
		}
	}

	// 根据当前模式更新模型信息
	const updateModelInfo = (modelInfo: any) => {
		if (currentMode === "plan") {
			handleFieldChange("planModeQaxModelInfo", modelInfo)
		} else {
			handleFieldChange("actModeQaxModelInfo", modelInfo)
		}
	}

	// Debounced function to refresh QAX models (prevents excessive API calls while typing)
	const debounceTimerRef = useRef<NodeJS.Timeout | null>(null)

	useEffect(() => {
		return () => {
			if (debounceTimerRef.current) {
				clearTimeout(debounceTimerRef.current)
			}
		}
	}, [])

	const debouncedRefreshQaxModels = useCallback((baseUrl?: string, apiKey?: string) => {
		if (debounceTimerRef.current) {
			clearTimeout(debounceTimerRef.current)
		}

		// Use default base URL if not provided
		const effectiveBaseUrl = baseUrl || getQaxAIPlatformBaseUrl()

		if (effectiveBaseUrl && apiKey) {
			debounceTimerRef.current = setTimeout(() => {
				setIsLoadingModels(true)

				QaxUtilsServiceClient.getQaxModels(
					OpenAiModelsRequest.create({
						baseUrl: effectiveBaseUrl,
						apiKey,
					}),
				)
					.then((response: any) => {
						setAvailableModels(response.values || [])
					})
					.catch((error: any) => {
						console.error("Failed to refresh QAX models:", error)
						setAvailableModels([])
					})
					.finally(() => {
						setIsLoadingModels(false)
					})
			}, 500)
		} else {
			setAvailableModels([])
		}
	}, [])

	// Load models on component mount if API key is available
	useEffect(() => {
		if (apiConfiguration?.qaxApiKey) {
			debouncedRefreshQaxModels(apiConfiguration.qaxBaseUrl || getQaxAIPlatformBaseUrl(), apiConfiguration.qaxApiKey)
		}
	}, [apiConfiguration?.qaxApiKey, apiConfiguration?.qaxBaseUrl, debouncedRefreshQaxModels])

	return (
		<div>
			<ApiKeyField
				initialValue={apiConfiguration?.qaxApiKey || ""}
				onChange={(value) => {
					handleFieldChange("qaxApiKey", value)
					debouncedRefreshQaxModels(apiConfiguration?.qaxBaseUrl || getQaxAIPlatformBaseUrl(), value)
				}}
				providerName="QAX"
			/>

			<BaseUrlField
				initialValue={apiConfiguration?.qaxBaseUrl || ""}
				onChange={(value) => {
					handleFieldChange("qaxBaseUrl", value)
					debouncedRefreshQaxModels(value || getQaxAIPlatformBaseUrl(), apiConfiguration?.qaxApiKey)
				}}
				label="Base URL"
				placeholder={getQaxAIPlatformBaseUrl()}
			/>

			{/* Model Selection */}
			<div style={{ marginBottom: 10 }}>
				<label style={{ fontWeight: 500, display: "block", marginBottom: 5 }}>Model</label>
				{availableModels.length > 0 ? (
					<VSCodeDropdown
						style={{ width: "100%" }}
						value={getCurrentModelId()}
						onChange={(e: any) => updateModelId(e.target.value)}>
						<VSCodeOption value="">Select a model...</VSCodeOption>
						{availableModels.map((model) => (
							<VSCodeOption key={model} value={model}>
								{model}
							</VSCodeOption>
						))}
					</VSCodeDropdown>
				) : (
					<DebouncedTextField
						initialValue={getCurrentModelId()}
						onChange={(value) => updateModelId(value)}
						style={{ width: "100%" }}
						placeholder={isLoadingModels ? "Loading models..." : "Enter Model ID..."}
					/>
				)}
			</div>

			{/* Model Configuration Toggle */}
			<div
				style={{
					color: getAsVar(VSC_DESCRIPTION_FOREGROUND),
					display: "flex",
					margin: "15px 0 10px 0",
					cursor: "pointer",
					alignItems: "center",
				}}
				onClick={() => setModelConfigurationSelected((val) => !val)}>
				<span
					className={`codicon ${modelConfigurationSelected ? "codicon-chevron-down" : "codicon-chevron-right"}`}
					style={{ marginRight: "4px" }}
				/>
				<span style={{ fontWeight: 500, textTransform: "uppercase" }}>Model Configuration</span>
			</div>

			{modelConfigurationSelected && (
				<>
					<VSCodeCheckbox
						checked={!!getCurrentModelInfo()?.supportsImages}
						onChange={(e: any) => {
							const isChecked = e.target.checked === true
							const modelInfo = getCurrentModelInfo() || { ...openAiModelInfoSaneDefaults }
							updateModelInfo({ ...modelInfo, supportsImages: isChecked })
						}}>
						Supports Images
					</VSCodeCheckbox>

					<VSCodeCheckbox
						checked={!!getCurrentModelInfo()?.isR1FormatRequired}
						onChange={(e: any) => {
							const isChecked = e.target.checked === true
							const modelInfo = getCurrentModelInfo() || { ...openAiModelInfoSaneDefaults }
							updateModelInfo({ ...modelInfo, isR1FormatRequired: isChecked })
						}}>
						Enable R1 messages format
					</VSCodeCheckbox>

					<div style={{ display: "flex", gap: 10, marginTop: "5px" }}>
						<DebouncedTextField
							initialValue={
								getCurrentModelInfo()?.contextWindow?.toString() ||
								openAiModelInfoSaneDefaults.contextWindow?.toString() ||
								""
							}
							style={{ flex: 1 }}
							onChange={(value) => {
								const modelInfo = getCurrentModelInfo() || { ...openAiModelInfoSaneDefaults }
								updateModelInfo({ ...modelInfo, contextWindow: Number(value) })
							}}>
							<span style={{ fontWeight: 500 }}>Context Window Size</span>
						</DebouncedTextField>

						<DebouncedTextField
							initialValue={
								getCurrentModelInfo()?.maxTokens?.toString() ||
								openAiModelInfoSaneDefaults.maxTokens?.toString() ||
								""
							}
							style={{ flex: 1 }}
							onChange={(value) => {
								const modelInfo = getCurrentModelInfo() || { ...openAiModelInfoSaneDefaults }
								updateModelInfo({ ...modelInfo, maxTokens: Number(value) })
							}}>
							<span style={{ fontWeight: 500 }}>Max Output Tokens</span>
						</DebouncedTextField>
					</div>

					<div style={{ display: "flex", gap: 10, marginTop: "5px" }}>
						<DebouncedTextField
							initialValue={
								getCurrentModelInfo()?.inputPrice?.toString() ||
								openAiModelInfoSaneDefaults.inputPrice?.toString() ||
								""
							}
							style={{ flex: 1 }}
							onChange={(value) => {
								const modelInfo = getCurrentModelInfo() || { ...openAiModelInfoSaneDefaults }
								updateModelInfo({ ...modelInfo, inputPrice: Number(value) })
							}}>
							<span style={{ fontWeight: 500 }}>Input Price / 1M tokens</span>
						</DebouncedTextField>

						<DebouncedTextField
							initialValue={
								getCurrentModelInfo()?.outputPrice?.toString() ||
								openAiModelInfoSaneDefaults.outputPrice?.toString() ||
								""
							}
							style={{ flex: 1 }}
							onChange={(value) => {
								const modelInfo = getCurrentModelInfo() || { ...openAiModelInfoSaneDefaults }
								updateModelInfo({ ...modelInfo, outputPrice: Number(value) })
							}}>
							<span style={{ fontWeight: 500 }}>Output Price / 1M tokens</span>
						</DebouncedTextField>
					</div>

					<div style={{ display: "flex", gap: 10, marginTop: "5px" }}>
						<DebouncedTextField
							initialValue={
								getCurrentModelInfo()?.temperature?.toString() ||
								openAiModelInfoSaneDefaults.temperature?.toString() ||
								""
							}
							onChange={(value) => {
								const modelInfo = getCurrentModelInfo() || { ...openAiModelInfoSaneDefaults }

								const shouldPreserveFormat = value.endsWith(".") || (value.includes(".") && value.endsWith("0"))

								const temperature =
									value === ""
										? openAiModelInfoSaneDefaults.temperature
										: shouldPreserveFormat
											? (value as any)
											: parseFloat(value)

								updateModelInfo({ ...modelInfo, temperature })
							}}>
							<span style={{ fontWeight: 500 }}>Temperature</span>
						</DebouncedTextField>
					</div>
				</>
			)}

			<p
				style={{
					fontSize: "12px",
					marginTop: 10,
					color: "var(--vscode-descriptionForeground)",
				}}>
				QAX 模型经过优化，能够提供高质量的代码生成和问题解决能力。
			</p>

			{showModelOptions && (
				<ModelInfoView selectedModelId={selectedModelId} modelInfo={selectedModelInfo} isPopup={isPopup} />
			)}
		</div>
	)
}
