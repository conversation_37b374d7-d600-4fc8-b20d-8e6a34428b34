import { AccountServiceClient } from "@/services/grpc-client"
import { QaxAccountServiceClient } from "@/services/grpc-client"
import { EmptyRequest } from "@shared/proto/cline/common"
import { QaxUserInfo } from "@shared/QaxUserInfo"
import React, { createContext, useCallback, useContext, useEffect, useState } from "react"
import { useExtensionState } from "./ExtensionStateContext"

// Define User type (you may need to adjust this based on your actual User type)
export interface ClineUser {
	uid: string
	email?: string
	displayName?: string
	photoUrl?: string
	appBaseUrl?: string
}

// QAX User type from proto
export type QaxUser = QaxUserInfo

export interface ClineAuthContextType {
	clineUser: ClineUser | null
	qaxUser: QaxUser | null
	handleSignIn: () => Promise<void>
	handleSignOut: () => Promise<void>
	handleQaxSignIn: () => Promise<void>
	handleQaxSignOut: () => Promise<void>
}

const ClineAuthContext = createContext<ClineAuthContextType | undefined>(undefined)

export const ClineAuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
	const { qaxUserInfo, setQaxUserInfo } = useExtensionState()
	const [user, setUser] = useState<ClineUser | null>(null)

	// Handle Cline auth status update events
	useEffect(() => {
		let cancelSubscription: (() => void) | undefined

		try {
			cancelSubscription = AccountServiceClient.subscribeToAuthStatusUpdate(EmptyRequest.create(), {
				onResponse: async (response: any) => {
					console.log("Extension: ClineAuthContext: Received auth status update:", response)
					if (response) {
						if (response.user) {
							setUser(response.user)
						} else {
							setUser(null)
						}
					}
				},
				onError: (error: Error) => {
					console.error("Error in auth callback subscription:", error)
				},
				onComplete: () => {
					console.log("Auth callback subscription completed")
				},
			})
		} catch (error) {
			console.error("Failed to set up auth status subscription:", error)
		}

		// Cleanup function to cancel subscription when component unmounts
		return () => {
			if (cancelSubscription) {
				cancelSubscription()
			}
		}
	}, [])

	// Handle QAX auth status update events
	useEffect(() => {
		let cancelQaxSubscription: (() => void) | undefined

		try {
			cancelQaxSubscription = QaxAccountServiceClient.subscribeToQaxAuthStatusUpdate(EmptyRequest.create(), {
				onResponse: async (response: any) => {
					if (response) {
						if (response.user) {
							setQaxUserInfo(response.user)
						} else {
							setQaxUserInfo(undefined)
						}
					}
				},
				onError: (error: Error) => {
					console.error("Error in QAX auth callback subscription:", error)
				},
				onComplete: () => {
					console.log("QAX auth callback subscription completed")
				},
			})
		} catch (error) {
			console.error("Failed to set up QAX auth status subscription:", error)
		}

		// Cleanup function to cancel subscription when component unmounts
		return () => {
			if (cancelQaxSubscription) {
				cancelQaxSubscription()
			}
		}
	}, [])

	const handleSignIn = useCallback(async () => {
		try {
			AccountServiceClient.accountLoginClicked(EmptyRequest.create()).catch((err) =>
				console.error("Failed to get login URL:", err),
			)
		} catch (error) {
			console.error("Error signing in:", error)
			throw error
		}
	}, [])

	const handleSignOut = useCallback(async () => {
		try {
			await AccountServiceClient.accountLogoutClicked(EmptyRequest.create()).catch((err) =>
				console.error("Failed to logout:", err),
			)
		} catch (error) {
			console.error("Error signing out:", error)
			throw error
		}
	}, [])

	const handleQaxSignIn = useCallback(async () => {
		try {
			await QaxAccountServiceClient.qaxLoginClicked(EmptyRequest.create()).catch((err: any) => {
				console.error("Failed to get QAX login URL:", err)
				throw err
			})
		} catch (error) {
			console.error("Error signing in to QAX:", error)
			throw error
		}
	}, [])

	const handleQaxSignOut = useCallback(async () => {
		try {
			await QaxAccountServiceClient.qaxLogoutClicked(EmptyRequest.create()).catch((err: any) => {
				console.error("Failed to logout from QAX:", err)
				throw err
			})
		} catch (error) {
			console.error("Error signing out from QAX:", error)
			throw error
		}
	}, [])

	return (
		<ClineAuthContext.Provider
			value={{
				clineUser: user,
				qaxUser: qaxUserInfo || null,
				handleSignIn,
				handleSignOut,
				handleQaxSignIn,
				handleQaxSignOut,
			}}>
			{children}
		</ClineAuthContext.Provider>
	)
}

export const useClineAuth = () => {
	const context = useContext(ClineAuthContext)
	if (context === undefined) {
		throw new Error("useClineAuth must be used within a ClineAuthProvider")
	}
	return context
}

// 便利的 hook 来访问 QAX 认证功能
export const useQaxAuth = () => {
	const context = useContext(ClineAuthContext)
	if (context === undefined) {
		throw new Error("useQaxAuth must be used within a ClineAuthProvider")
	}
	return {
		qaxUser: context.qaxUser,
		handleSignIn: context.handleQaxSignIn,
		handleSignOut: context.handleQaxSignOut,
	}
}
