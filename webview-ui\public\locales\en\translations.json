{"qax": {"enhancePrompt": {"button": "✨", "tooltip": "Qax Enhance Prompt", "enhancing": "Enhancing..."}, "memoryWorkspace": {"memories": "Memories", "memoriesTitle": "Qax Codegen Memories", "currentWorkspace": "Current workspace"}, "settings": {"autocomplete": {"title": "Autocomplete", "enable": "Enable Autocomplete", "delay": "Delay (ms)", "maxLines": "Max Lines", "triggerCharacters": "Trigger Characters", "enableQaxAutocomplete": "Enable QAX Autocomplete", "provider": "Provider", "openaiCompatible": "OpenAI Compatible", "fim": "FIM (Fill in the Middle)", "fimApi": "FIM API", "openaiCompatibleApi": "OpenAI Compatible API", "fimBaseUrl": "FIM Base URL", "apiBaseUrl": "API Base URL", "modelId": "Model ID", "maxTokens": "<PERSON>", "temperature": "Temperature", "timeout": "Timeout (ms)", "debounce": "De<PERSON>un<PERSON> (ms)", "usePromptCache": "Use Prompt Cache", "note": "Note", "noteDescription": "QAX Autocomplete provides intelligent code completion using AI models. Choose between:\nOpenAI Compatible: Works with OpenRouter, OpenAI, Azure OpenAI, local models (Ollama, LM Studio), and other OpenAI-compatible services.\nFIM (Fill in the Middle): Specialized API format that sends both prefix and suffix context for more accurate completions.\nConfigure your provider settings to enable intelligent code suggestions based on your current context."}, "general": {"title": "Settings", "preferredLanguage": "Preferred Language", "english": "English", "simplifiedChinese": "Simplified Chinese - 简体中文", "done": "Done", "allowAnonymousReporting": "Allow anonymous error and usage reporting", "allowAnonymousReportingDescription": "Help improve Cline by sending anonymous usage data and error reports. No code, prompts, or personal information are ever sent. See our", "telemetryOverview": "telemetry overview", "and": "and", "privacyPolicy": "privacy policy", "forMoreDetails": "for more details.", "languageDescription": "The language that C<PERSON> should use for communication."}, "tabs": {"apiConfiguration": "API Configuration", "autocomplete": "Autocomplete", "autocompleteSettings": "Autocomplete Settings", "qaxAutocompleteSettings": "QAX Autocomplete Settings", "general": "General", "generalSettings": "General Settings", "features": "Features", "featureSettings": "Feature Settings", "browser": "Browser", "browserSettings": "Browser <PERSON>s", "terminal": "Terminal", "terminalSettings": "Terminal Settings", "debug": "Debug", "debugTools": "Debug Tools", "debugHeader": "Debug", "about": "About", "aboutCline": "About Cline", "aboutHeader": "About"}, "api": {"Provider": "API Provider", "planMode": "Plan Mode", "actMode": "Act Mode", "useDifferentModels": "Use different models for Plan and Act modes", "useDifferentModelsDescription": "Switching between Plan and Act mode will persist the API and model used in the previous mode. This may be helpful e.g. when using a strong reasoning model to architect a plan for a cheaper coding model to act on."}, "features": {"enableCheckpoints": "Enable Checkpoints", "enableCheckpointsDescription": "Enables extension to save checkpoints of workspace throughout the task. Uses git under the hood which may not work well with large workspaces.", "enableMcpMarketplace": "Enable MCP Marketplace", "enableMcpMarketplaceDescription": "Enables the MCP Marketplace tab for discovering and installing MCP servers.", "mcpDisplayMode": "MCP Display Mode", "mcpDisplayModeDescription": "Controls how MCP responses are displayed: plain text, rich formatting with links/images, or markdown rendering.", "collapseMcpResponses": "Collapse MCP Responses", "collapseMcpResponsesDescription": "Sets the default display mode for MCP response panels", "plainText": "Plain Text", "richDisplay": "<PERSON>", "markdown": "<PERSON><PERSON>", "openaiReasoningEffort": "OpenAI Reasoning Effort", "low": "Low", "medium": "Medium", "high": "High", "openaiReasoningEffortDescription": "Reasoning effort for the OpenAI family of models (applies to all OpenAI model providers)"}, "browser": {"disableBrowserToolUsage": "Disable browser tool usage", "disableBrowserToolUsageDescription": "Prevent Cline from using browser actions (e.g. launch, click, type).", "viewportSize": "Viewport size", "viewportSizeDescription": "Set the size of the browser viewport for screenshots and interactions.", "useRemoteBrowserConnection": "Use remote browser connection", "chromeDescription": "Enable Cline to use your Chrome{isBundled}{hasRemoteBrowserEnabled, select, true { manually (--remote-debugging-port=9222) or using the button below. Enter the host address or leave it blank for automatic discovery.} other {.}}", "notDetected": "(not detected on your machine)", "chromeExecutablePath": "Chrome Executable Path (Optional)", "chromeExecutablePathDescription": "Leave blank to auto-detect.", "launchingBrowser": "Launching Browser...", "launchBrowserWithDebugMode": "Launch Browser with Debug Mode"}, "terminal": {"defaultTerminalProfile": "Default Terminal Profile", "defaultTerminalProfileDescription": "Select the default terminal C<PERSON> will use. 'Default' uses your VSCode global setting.", "shellIntegrationTimeout": "Shell integration timeout (seconds)", "enterTimeout": "Enter timeout in seconds", "shellIntegrationTimeoutDescription": "Set how long <PERSON><PERSON> waits for shell integration to activate before executing commands. Increase this value if you experience terminal connection timeouts.", "enableAggressiveTerminalReuse": "Enable aggressive terminal reuse", "enableAggressiveTerminalReuseDescription": "When enabled, Cline will reuse existing terminal windows that aren't in the current working directory. Disable this if you experience issues with task lockout after a terminal command.", "outputLimit": "Terminal output limit", "outputLimitDescription": "Maximum number of lines to include in terminal output when executing commands. When exceeded, lines will be removed from the middle, saving tokens."}, "note": "Note", "noteDescription": "QAX Autocomplete provides intelligent code completion using AI models. Choose between:\nOpenAI Compatible: Works with OpenRouter, OpenAI, Azure OpenAI, local models (Ollama, LM Studio), and other OpenAI-compatible services.\nFIM (Fill in the Middle): Specialized API format that sends both prefix and suffix context for more accurate completions.\nConfigure your provider settings to enable intelligent code suggestions based on your current context."}, "todo": {"title": "Todo List", "addTask": "Add Task", "empty": "No tasks yet", "completed": "Completed", "addTodoPrompt": "Enter todo item:", "delete": "Delete todo", "deleteConfirm": "Are you sure you want to delete this todo item?"}, "account": {"title": "QAX Account", "done": "Done", "loginPrompt": "Please log in to your QAX account to view user information and use QAX Codegen services.", "loginButton": "Log in to QAX Account", "tokenStatus": "TOKEN Status", "expirationTime": "Expiration Time", "tokenExpired": "Token has expired, please log in again", "tokenExpiringSoon": "Token will expire within 24 hours", "tokenValid": "Token is valid", "logout": "Log out"}, "mcp": {"title": "MCP Servers", "done": "Done", "marketplace": "Marketplace", "remoteServers": "Remote Servers", "installed": "Installed", "searchPlaceholder": "Search MCPs...", "filter": "Filter:", "allCategories": "All Categories", "sort": "Sort:", "mostInstalls": "Most Installs", "newest": "Newest", "githubStars": "GitHub Stars", "name": "Name", "noMatchingServers": "No matching MCP servers found", "noServersFound": "No MCP servers found in the marketplace", "installing": "Installing...", "install": "Install", "requiresApiKey": "Requires API key", "loadingPreview": "Loading preview for", "waitingFor": "Waiting for", "unableToLoadPreview": "Unable to load preview", "previewTimeout": "Preview request timed out", "networkError": "Network error loading preview", "clickToOpen": "Click to open in browser", "noTitle": "No title", "noDescription": "No description available", "loadingImage": "Loading image from", "failedToLoadImage": "Failed to load image", "addRemoteServer": "Add a remote MCP server by providing a name and its URL endpoint. Learn more", "here": "here.", "serverName": "Server Name", "serverUrl": "Server URL", "serverNameRequired": "Server name is required", "serverUrlRequired": "Server URL is required", "invalidUrl": "Invalid URL format", "adding": "Adding...", "addServer": "Add Server", "connecting": "Connecting to server... This may take a few seconds.", "editConfiguration": "Edit Configuration", "response": "Response", "responseError": "Response (Error)", "errorParsingResponse": "Error parsing response:"}, "telemetry": {"closeBanner": "Close banner", "helpImproveCline": "Help improve Cline by sending anonymous usage data and error reports.", "experimentalFeatures": "Experimental features may be enabled.", "dataCollection": "No code, prompts, or personal information are ever sent.", "turnOffSetting": "You can turn this off at any time in the", "settings": "settings"}, "chat": {"creditLimit": {"outOfCredit": "You've run out of credits!", "currentBalance": "Current Balance", "totalSpent": "Total Spent", "totalPromotions": "Total Promotions", "buyCredits": "Buy Credits", "retryRequest": "Retry Request"}, "quote": {"selection": "Quote selection", "selectionInReply": "Quote selection in reply"}}, "clineRules": {"manageRulesAndWorkflows": "Manage Cline Rules and Workflows", "clineRules": "Cline Rules", "rules": "Rules", "workflows": "Workflows", "rulesDescription": "Cline Rules are markdown files that contain instructions and context that Cline will always follow. They are automatically included in every task. Learn more about Cline Rules in the", "workflowsDescription1": "Workflows are slash commands that can be triggered by typing", "workflowName": "/workflow-name", "workflowsDescription2": "in the chat. They can be used to automate common tasks or provide quick access to frequently used prompts. Learn more about Workflows in the", "docs": "documentation", "globalRules": "Global Rules", "workspaceRules": "Workspace Rules", "globalWorkflows": "Global Workflows", "workspaceWorkflows": "Workspace Workflows"}, "browser": {"connectionInfo": "Browser connection info", "connection": "Connection", "status": "Status", "connected": "Connected", "disconnected": "Disconnected", "type": "Type", "remote": "Remote", "local": "Local", "remoteHost": "Remote Host"}, "history": {"title": "History", "done": "Done", "searchPlaceholder": "Search tasks...", "newest": "Newest", "oldest": "Oldest", "mostExpensive": "Most Expensive", "mostTokens": "Most Tokens", "mostRelevant": "Most Relevant", "workspace": "Workspace", "favorites": "Favorites", "selectAll": "Select All", "selectNone": "Select None", "export": "Export", "deleteSelected": "Delete Selected", "selected": "Selected", "deleteAll": "Delete All"}, "welcome": {"title": "Welcome to Cline", "description1": "<PERSON><PERSON> is an AI coding assistant powered by", "description2": "— the world's most advanced AI for code. <PERSON>line can understand your entire codebase and perform complex tasks that would take you hours or days to complete on your own.", "description3": "currently in beta", "description4": "Cline can understand your entire codebase and perform complex tasks that would take you hours or days to complete on your own.", "signup": "Sign up for a Cline account to get started with AI coding assistance.", "getStarted": "Get Started", "useApiKey": "Use API Key Instead", "letsGo": "Let's Go!"}, "about": {"questionsOrFeedback": "If you have any questions or feedback, feel free to open an issue at"}, "debug": {"resetWorkspaceState": "Reset Workspace State", "resetGlobalState": "Reset Global State", "resetStateDescription": "This will reset all global state and secret storage in the extension."}}}