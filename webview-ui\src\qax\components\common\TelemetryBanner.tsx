import { StateServiceClient } from "@/services/grpc-client"
import { TelemetrySettingEnum, TelemetrySettingRequest } from "@shared/proto/cline/state"
import { memo } from "react"
import styled from "styled-components"
import { useTranslation } from "@/utils/i18n"

const BannerContainer = styled.div`
	background-color: var(--vscode-banner-background);
	padding: 12px 20px;
	display: flex;
	flex-direction: column;
	gap: 10px;
	flex-shrink: 0;
	margin-bottom: 6px;
	position: relative;
`

const CloseButton = styled.button`
	position: absolute;
	top: 12px;
	right: 12px;
	background: none;
	border: none;
	color: var(--vscode-foreground);
	cursor: pointer;
	font-size: 16px;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 4px;
	opacity: 0.7;
	&:hover {
		opacity: 1;
	}
`

const ButtonContainer = styled.div`
	display: flex;
	gap: 8px;
	width: 100%;

	& > vscode-button {
		flex: 1;
	}
`

const QaxTelemetryBanner = () => {
	const { t } = useTranslation()
	const handleClose = async () => {
		try {
			await StateServiceClient.updateTelemetrySetting(
				TelemetrySettingRequest.create({
					setting: TelemetrySettingEnum.ENABLED,
				}),
			)
		} catch (error) {
			console.error("Error updating telemetry setting:", error)
		}
	}

	return (
		<BannerContainer>
			<CloseButton onClick={handleClose} aria-label={t("qax.telemetry.closeBanner")}>
				✕
			</CloseButton>
			<div>
				<strong>{t("qax.telemetry.helpImprove")}</strong>
				<i>
					<br />
					{t("qax.telemetry.experimentalFeatures")}
				</i>
				<div style={{ marginTop: 4 }}>
					{t("qax.telemetry.description")}
					<div style={{ marginTop: 4 }}>{t("qax.telemetry.startConversation")}</div>
				</div>
			</div>
		</BannerContainer>
	)
}

export default memo(QaxTelemetryBanner)
