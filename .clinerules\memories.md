# Localization
- 继续推进国际化翻译与修改工作，涵盖本地化文件加载、硬编码扫描及翻译内容补充等已知待办事项。
- 用户主动询问@/webview-ui/src/qax/components/todo/QaxTodoList.tsx文件是否存在硬编码字符串，需将该文件纳入持续扫描范围并验证其国际化完整性
- 发现qax.settings.debug.resetWorkspaceState和qax.settings.debug.resetGlobalState两个调试设置项缺少翻译，需补充到翻译文件
- 发现用户界面字符串"The language that Cline should use for communication."缺少翻译，需补充到翻译文件
- 用户指出clineRules.rules键值位置存在层级结构错误，需要重新校验键值路径与translation文件的匹配性

# Testing
- 单元测试在VSCode Webview环境中因协议限制无法通过fetch访问本地翻译文件，需使用VSCode的webview.asWebviewUri或postMessage机制，并补充window.localesBaseUri类型声明
- 用户关注在单元测试中验证不同语言的翻译内容是否能正确获取，并发现界面点击无反应的问题
- 随着国际化工作的推进，需同步完善测试覆盖，验证多语言支持及Webview环境下的翻译加载机制。

# TypeScript Configuration
- VSCode Webview测试环境中window对象缺少localesBaseUri属性声明导致TS2339错误，需扩展Window接口类型定义

# Localization Tools
- 在持续进行的国际化工作中，需确保自动化工具支持代码与翻译文件的双向同步，以维持实时一致性。

# Code Style
- 国际化 key 命名规范：使用 kebab-case，最长 64 字符，缺失翻译用 TODO 占位

# Task Execution
- 用户报告之前的replace任务执行失败，文件内容未发生变化，需要检查任务执行机制和文件修改流程
- 用户指出JSON文件存在语法错误需要修复，可能涉及翻译文件或配置文件的校验与处理流程
- 用户指出存在lint错误并建议使用diagnose工具进行检查，需结合工具排查代码及翻译文件中的潜在问题。
