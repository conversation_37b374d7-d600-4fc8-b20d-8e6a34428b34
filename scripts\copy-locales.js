import { copyFileSync, existsSync, mkdirSync, readdirSync, statSync } from "fs"
import { join, resolve } from "path"

// 复制文件夹的递归函数
function copyFolderRecursiveSync(source, target) {
	// 检查源文件夹是否存在
	if (!existsSync(source)) {
		console.log(`Source folder ${source} does not exist`)
		return
	}

	// 创建目标文件夹
	if (!existsSync(target)) {
		mkdirSync(target, { recursive: true })
	}

	// 读取源文件夹内容
	const files = readdirSync(source)

	files.forEach((file) => {
		const sourcePath = join(source, file)
		const targetPath = join(target, file)

		if (statSync(sourcePath).isDirectory()) {
			// 递归复制子文件夹
			copyFolderRecursiveSync(sourcePath, targetPath)
		} else {
			// 复制文件
			copyFileSync(sourcePath, targetPath)
			console.log(`Copied ${sourcePath} to ${targetPath}`)
		}
	})
}

// 获取项目根目录
const rootDir = resolve(process.cwd(), process.cwd().includes("webview-ui") ? ".." : "")
const sourceLocalesDir = join(rootDir, "locales")
const targetLocalesDir = join(rootDir, "webview-ui", "public", "locales")

console.log("Copying locales from:", sourceLocalesDir)
console.log("Copying locales to:", targetLocalesDir)

// 复制 locales 目录
copyFolderRecursiveSync(sourceLocalesDir, targetLocalesDir)

console.log("Locales copied successfully!")
