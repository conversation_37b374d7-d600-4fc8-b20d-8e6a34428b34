import { useExtensionState } from "@/context/ExtensionStateContext"
import React from "react"

// 支持的语言列表
export const SUPPORTED_LANGUAGES = [
	{ code: "en", name: "English", nativeName: "English" },
	{ code: "zh-cn", name: "Simplified Chinese", nativeName: "简体中文" },
] as const

export type LanguageCode = (typeof SUPPORTED_LANGUAGES)[number]["code"]

// 语言资源类型定义
export interface TranslationResources {
	[key: string]: string | TranslationResources
}

// 语言资源缓存
const resourceCache: Record<string, TranslationResources> = {}
// 加载状态缓存，避免重复加载
const loadingStates: Record<string, Promise<TranslationResources> | null> = {}

// 加载语言资源
export async function loadLanguageResources(languageCode: string): Promise<TranslationResources> {
	console.log("[i18n] loadLanguageResources called for:", languageCode)
	// 如果缓存中已有资源，直接返回
	if (resourceCache[languageCode]) {
		console.log("[i18n] Using cached resources for:", languageCode)
		return resourceCache[languageCode]
	}

	// 如果正在加载中，返回加载Promise
	if (loadingStates[languageCode]) {
		return loadingStates[languageCode]!
	}

	// 创建加载Promise
	const loadPromise = (async () => {
		try {
			// 在VSCode Webview环境中，使用webview URI来访问翻译文件
			// 检查是否有注入的localesBaseUri（来自webview）
			const localesBaseUri = window.localesBaseUri

			let paths: string[] = []

			if (localesBaseUri) {
				// 在VSCode webview环境中，使用注入的webview URI
				paths = [`${localesBaseUri}/${languageCode}/translations.json`]
				console.log(`[i18n] Using webview URI for ${languageCode}:`, paths[0])
			} else {
				// 在开发环境或其他环境中，使用相对路径
				paths = [`./locales/${languageCode}/translations.json`, `locales/${languageCode}/translations.json`]
				console.log(`[i18n] Using relative paths for ${languageCode}:`, paths)
			}

			for (const path of paths) {
				try {
					console.log(`[i18n] Trying to fetch from: ${path}`)
					const response = await fetch(path)
					console.log(`[i18n] Fetch response for ${languageCode} from ${path}:`, response.status)
					if (response.ok) {
						const resources = await response.json()
						console.log(`[i18n] Successfully loaded translations for ${languageCode} from ${path}`)
						// 缓存资源
						resourceCache[languageCode] = resources
						return resources
					}
				} catch (error) {
					console.warn(`[i18n] Failed to fetch translations for ${languageCode} from ${path}:`, error)
				}
			}

			// 如果所有路径都失败，尝试使用内嵌的默认翻译
			console.warn(`[i18n] All fetch attempts failed for ${languageCode}, using fallback`)
			const fallbackResources = getFallbackTranslations(languageCode)
			resourceCache[languageCode] = fallbackResources
			return fallbackResources
		} catch (error) {
			console.warn(`[i18n] Failed to load translations for ${languageCode}:`, error)
		}

		// 如果加载失败，返回空对象
		console.log(`[i18n] Returning empty resources for ${languageCode}`)
		return {}
	})()

	loadingStates[languageCode] = loadPromise

	try {
		const resources = await loadPromise
		// 加载完成后清除加载状态
		loadingStates[languageCode] = null
		return resources
	} catch (error) {
		// 加载失败时也清除加载状态
		loadingStates[languageCode] = null
		console.error(`[i18n] Failed to load resources for ${languageCode}:`, error)
		throw error
	}
}

// 获取内嵌的默认翻译
function getFallbackTranslations(languageCode: string): TranslationResources {
	const fallbacks: Record<string, TranslationResources> = {
		en: {
			qax: {
				settings: {
					general: {
						title: "Settings",
						done: "Done",
					},
					tabs: {
						apiConfiguration: "API Configuration",
						autocomplete: "Autocomplete",
						general: "General",
						features: "Features",
						browser: "Browser",
						terminal: "Terminal",
						debug: "Debug",
						about: "About",
					},
				},
			},
		},
		"zh-cn": {
			qax: {
				settings: {
					general: {
						title: "设置",
						done: "完成",
					},
					tabs: {
						apiConfiguration: "API配置",
						autocomplete: "自动补全",
						general: "通用",
						features: "功能",
						browser: "浏览器",
						terminal: "终端",
						debug: "调试",
						about: "关于",
					},
				},
			},
		},
	}

	return fallbacks[languageCode] || fallbacks["en"]
}

// 获取翻译文本
export function t(key: string, language: string = "en", params?: Record<string, string | number>): string {
	const keys = key.split(".")
	let current: any = resourceCache[language]

	// 如果当前语言资源不存在，尝试使用英文作为后备
	if (!current && language !== "en") {
		current = resourceCache["en"]
		// 如果英文资源也不存在，尝试使用内嵌的后备翻译
		if (!current) {
			current = getFallbackTranslations("en")
		}
	}

	// 如果当前语言资源不存在，使用内嵌的后备翻译
	if (!current) {
		current = getFallbackTranslations(language)
	}

	// 遍历键路径查找翻译
	for (const k of keys) {
		if (current && typeof current === "object" && k in current) {
			current = current[k]
		} else {
			// 如果找不到翻译，尝试使用英文后备
			if (language !== "en") {
				return t(key, "en", params)
			}
			// 如果英文也找不到，返回键名作为默认值
			current = key
			break
		}
	}

	// 如果找到的是字符串且有参数，则进行替换
	if (typeof current === "string" && params) {
		let result = current
		for (const [paramKey, paramValue] of Object.entries(params)) {
			result = result.replace(new RegExp(`{{${paramKey}}}`, "g"), String(paramValue))
		}
		return result
	}

	return typeof current === "string" ? current : key
}

// React Hook 用于在组件中使用翻译
export function useTranslation() {
	console.log("[i18n] useTranslation called")
	const { chatSettings } = useExtensionState()
	const currentLanguage = chatSettings?.preferredLanguage || "English"
	console.log("[i18n] Current language from chatSettings:", currentLanguage)

	// 将语言名称转换为代码
	const languageCode = getLanguageCode(currentLanguage)
	console.log("[i18n] Language code:", languageCode)
	const [isLoaded, setIsLoaded] = React.useState(!!resourceCache[languageCode])
	console.log("[i18n] Initial isLoaded state:", isLoaded)

	// 确保语言资源已加载
	React.useEffect(() => {
		let cancelled = false

		const loadResources = async () => {
			try {
				// 首先确保英文资源已加载（作为后备）
				if (!resourceCache["en"]) {
					await loadLanguageResources("en")
				}

				// 然后加载目标语言资源
				if (languageCode !== "en") {
					await loadLanguageResources(languageCode)
				}

				if (!cancelled) {
					setIsLoaded(true)
				}
			} catch (error) {
				console.error(`Failed to load language resources for ${languageCode}:`, error)
				if (!cancelled) {
					// 即使加载失败，也设置为已加载，因为我们有内嵌的后备翻译
					setIsLoaded(true)
				}
			}
		}

		// 重置加载状态
		setIsLoaded(false)
		loadResources()

		return () => {
			cancelled = true
		}
	}, [languageCode])

	const translate = React.useCallback(
		(key: string, params?: Record<string, string | number>) => {
			return t(key, languageCode, params)
		},
		[languageCode],
	)

	return {
		t: translate,
		language: languageCode,
		isLoaded,
	}
}

// 将语言名称转换为代码
export function getLanguageCode(languageName: string): string {
	const languageMap: Record<string, string> = {
		English: "en",
		"Arabic - العربية": "ar",
		"Portuguese - Português (Brasil)": "pt-br",
		"Czech - Čeština": "cs",
		"French - Français": "fr",
		"German - Deutsch": "de",
		"Hindi - हिन्दी": "hi",
		"Hungarian - Magyar": "hu",
		"Italian - Italiano": "it",
		"Japanese - 日本語": "ja",
		"Korean - 한국어": "ko",
		"Polish - Polski": "pl",
		"Portuguese - Português (Portugal)": "pt",
		"Russian - Русский": "ru",
		"Simplified Chinese - 简体中文": "zh-cn",
		"Spanish - Español": "es",
		"Traditional Chinese - 繁體中文": "zh-tw",
		"Turkish - Türkçe": "tr",
	}

	return languageMap[languageName] || "en"
}

// 将语言代码转换为名称
export function getLanguageName(languageCode: string): string {
	const nameMap: Record<string, string> = {
		en: "English",
		ar: "Arabic - العربية",
		"pt-br": "Portuguese - Português (Brasil)",
		cs: "Czech - Čeština",
		fr: "French - Français",
		de: "German - Deutsch",
		hi: "Hindi - हिन्दी",
		hu: "Hungarian - Magyar",
		it: "Italian - Italiano",
		ja: "Japanese - 日本語",
		ko: "Korean - 한국어",
		pl: "Polish - Polski",
		pt: "Portuguese - Português (Portugal)",
		ru: "Russian - Русский",
		"zh-cn": "Simplified Chinese - 简体中文",
		es: "Spanish - Español",
		"zh-tw": "Traditional Chinese - 繁體中文",
		tr: "Turkish - Türkçe",
	}

	return nameMap[languageCode] || "English"
}
