import { useExtensionState } from "@/context/ExtensionStateContext"
import React from "react"

// 支持的语言列表
export const SUPPORTED_LANGUAGES = [
	{ code: "en", name: "English", nativeName: "English" },
	{ code: "zh-cn", name: "Simplified Chinese", nativeName: "简体中文" },
] as const

export type LanguageCode = (typeof SUPPORTED_LANGUAGES)[number]["code"]

// 语言资源类型定义
export interface TranslationResources {
	[key: string]: string | TranslationResources
}

// 语言资源缓存
const resourceCache: Record<string, TranslationResources> = {}
// 加载状态缓存，避免重复加载
const loadingStates: Record<string, Promise<TranslationResources> | null> = {}

// 加载语言资源
export async function loadLanguageResources(languageCode: string): Promise<TranslationResources> {
	console.log("[i18n] loadLanguageResources called for:", languageCode)
	// 如果缓存中已有资源，直接返回
	if (resourceCache[languageCode]) {
		console.log("[i18n] Using cached resources for:", languageCode)
		return resourceCache[languageCode]
	}

	// 如果正在加载中，返回加载Promise
	if (loadingStates[languageCode]) {
		return loadingStates[languageCode]!
	}

	// 创建加载Promise
	const loadPromise = (async () => {
		try {
			// 在VSCode Webview环境中，使用webview URI来访问翻译文件
			// 检查是否有注入的localesBaseUri（来自webview）
			const localesBaseUri = window.localesBaseUri

			let paths: string[] = []

			if (localesBaseUri) {
				// 在VSCode webview环境中，使用注入的webview URI
				paths = [`${localesBaseUri}/${languageCode}/translations.json`]
				console.log(`[i18n] Using webview URI for ${languageCode}:`, paths[0])
			} else {
				// 在开发环境或其他环境中，使用相对路径
				paths = [`./locales/${languageCode}/translations.json`, `locales/${languageCode}/translations.json`]
				console.log(`[i18n] Using relative paths for ${languageCode}:`, paths)
			}

			for (const path of paths) {
				try {
					console.log(`[i18n] Trying to fetch from: ${path}`)
					const response = await fetch(path)
					console.log(`[i18n] Fetch response for ${languageCode} from ${path}:`, response.status)
					if (response.ok) {
						const resources = await response.json()
						console.log(`[i18n] Successfully loaded translations for ${languageCode} from ${path}`)
						// 缓存资源
						resourceCache[languageCode] = resources
						return resources
					}
				} catch (error) {
					console.warn(`[i18n] Failed to fetch translations for ${languageCode} from ${path}:`, error)
				}
			}

			// 如果所有路径都失败，尝试使用内嵌的默认翻译
			console.warn(`[i18n] All fetch attempts failed for ${languageCode}, using fallback`)
			const fallbackResources = getFallbackTranslations(languageCode)
			resourceCache[languageCode] = fallbackResources
			return fallbackResources
		} catch (error) {
			console.warn(`[i18n] Failed to load translations for ${languageCode}:`, error)
		}

		// 如果加载失败，返回空对象
		console.log(`[i18n] Returning empty resources for ${languageCode}`)
		return {}
	})()

	loadingStates[languageCode] = loadPromise

	try {
		const resources = await loadPromise
		// 加载完成后清除加载状态
		loadingStates[languageCode] = null
		return resources
	} catch (error) {
		// 加载失败时也清除加载状态
		loadingStates[languageCode] = null
		console.error(`[i18n] Failed to load resources for ${languageCode}:`, error)
		throw error
	}
}

// 获取内嵌的默认翻译
function getFallbackTranslations(languageCode: string): TranslationResources {
	const fallbacks: Record<string, TranslationResources> = {
		en: {
			welcome: {
				title: "Welcome to Cline",
				description1: "Cline is an AI coding assistant powered by",
				description2: "— the world's most advanced AI for code. Cline can understand your entire codebase and perform complex tasks that would take you hours or days to complete on your own.",
				description3: "currently in beta",
				description4: "Cline can understand your entire codebase and perform complex tasks that would take you hours or days to complete on your own.",
				signup: "Sign up for a Cline account to get started with AI coding assistance.",
				getStarted: "Get Started",
				useApiKey: "Use API Key Instead",
				letsGo: "Let's Go!"
			},
			mcp: {
				title: "MCP Servers",
				done: "Done",
				marketplace: "Marketplace",
				remoteServers: "Remote Servers",
				installed: "Installed",
				searchPlaceholder: "Search MCPs...",
				filter: "Filter:",
				allCategories: "All Categories",
				sort: "Sort:",
				mostInstalls: "Most Installs",
				newest: "Newest",
				githubStars: "GitHub Stars",
				name: "Name",
				noMatchingServers: "No matching MCP servers found",
				noServersFound: "No MCP servers found in the marketplace",
				installing: "Installing...",
				install: "Install",
				requiresApiKey: "Requires API key",
				loadingPreview: "Loading preview for",
				waitingFor: "Waiting for",
				clickToOpen: "Click to open in browser",
				noTitle: "No title",
				noDescription: "No description available",
				loadingImage: "Loading image from",
				failedToLoadImage: "Failed to load image",
				addRemoteServer: "Add a remote MCP server by providing a name and its URL endpoint. Learn more",
				here: "here.",
				serverName: "Server Name",
				serverUrl: "Server URL",
				serverNameRequired: "Server name is required",
				serverUrlRequired: "Server URL is required"
			},
			clineRules: {
				manageRulesAndWorkflows: "Manage Cline Rules and Workflows",
				clineRules: "Cline Rules",
				rules: "Rules",
				workflows: "Workflows",
				rulesDescription: "Cline Rules are markdown files that contain instructions and context that Cline will always follow. They are automatically included in every task. Learn more about Cline Rules in the",
				workflowsDescription1: "Workflows are slash commands that can be triggered by typing",
				workflowName: "/workflow-name",
				workflowsDescription2: "in the chat. They can be used to automate common tasks or provide quick access to frequently used prompts. Learn more about Workflows in the",
				docs: "documentation",
				globalRules: "Global Rules",
				workspaceRules: "Workspace Rules",
				globalWorkflows: "Global Workflows",
				workspaceWorkflows: "Workspace Workflows"
			},
			qax: {
				enhancePrompt: {
					button: "✨",
					tooltip: "Qax Enhance Prompt",
					enhancing: "Enhancing..."
				},
				memoryWorkspace: {
					memories: "Memories",
					memoriesTitle: "Qax Codegen Memories",
					currentWorkspace: "Current workspace"
				},
				settings: {
					autocomplete: {
						title: "Autocomplete",
						enable: "Enable Autocomplete",
						delay: "Delay (ms)",
						maxLines: "Max Lines",
						triggerCharacters: "Trigger Characters",
						enableQaxAutocomplete: "Enable QAX Autocomplete",
						provider: "Provider",
						openaiCompatible: "OpenAI Compatible",
						fim: "FIM (Fill in the Middle)",
						fimApi: "FIM API",
						openaiCompatibleApi: "OpenAI Compatible API",
						temperature: "Temperature",
						timeout: "Timeout (ms)",
						debounce: "Debounce (ms)",
						usePromptCache: "Use Prompt Cache",
						note: "Note",
						noteDescription: "QAX Autocomplete provides intelligent code completion using AI models. Choose between:\nOpenAI Compatible: Works with OpenRouter, OpenAI, Azure OpenAI, local models (Ollama, LM Studio), and other OpenAI-compatible services.\nFIM (Fill in the Middle): Specialized API format that sends both prefix and suffix context for more accurate completions.\nConfigure your provider settings to enable intelligent code suggestions based on your current context."
					},
					general: {
						title: "Settings",
						done: "Done",
						preferredLanguage: "Preferred Language",
						english: "English",
						simplifiedChinese: "Simplified Chinese - 简体中文",
						languageDescription: "Select your preferred language for the interface"
					},
					tabs: {
						apiConfiguration: "API Configuration",
						autocomplete: "Autocomplete",
						qaxAutocompleteSettings: "QAX Autocomplete Settings",
						general: "General",
						features: "Features",
						browser: "Browser",
						terminal: "Terminal",
						debug: "Debug",
						about: "About"
					}
				},
				todo: {
					title: "Todo List",
					addTask: "Add Task",
					empty: "No tasks yet",
					addTodoPrompt: "Enter todo item:",
					delete: "Delete todo",
					deleteConfirm: "Are you sure you want to delete this todo item?"
				},
				account: {
					title: "QAX Account",
					done: "Done",
					loginPrompt: "Please log in to your QAX account to view user information and use QAX Codegen services.",
					loginButton: "Log in to QAX Account",
					tokenStatus: "TOKEN Status",
					expirationTime: "Expiration Time",
					tokenExpired: "Token has expired, please log in again",
					tokenExpiringSoon: "Token will expire within 24 hours",
					tokenValid: "Token is valid",
					logout: "Log out"
				},
				telemetry: {
					helpImprove: "Help improve QAX Codegen",
					experimentalFeatures: "Experimental features",
					description: "We collect anonymous usage data to improve the extension.",
					startConversation: "Start a conversation to begin using QAX Codegen.",
					closeBanner: "Close banner"
				},
				about: {
					questionsOrFeedback: "Questions or feedback?"
				}
			},
		},
		"zh-cn": {
			welcome: {
				title: "欢迎使用 Cline",
				description1: "Cline 是一个由",
				description2: "驱动的 AI 编码助手——世界上最先进的代码 AI。Cline 可以理解您的整个代码库并执行复杂的任务，这些任务如果由您自己完成可能需要数小时或数天时间。",
				description3: "目前处于测试阶段",
				description4: "Cline 可以理解您的整个代码库并执行复杂的任务，这些任务如果由您自己完成可能需要数小时或数天时间。",
				signup: "注册 Cline 账户以开始使用 AI 编码助手。",
				getStarted: "开始使用",
				useApiKey: "改用 API 密钥",
				letsGo: "开始吧！"
			},
			mcp: {
				title: "MCP 服务器",
				done: "完成",
				marketplace: "市场",
				remoteServers: "远程服务器",
				installed: "已安装",
				searchPlaceholder: "搜索 MCP...",
				filter: "筛选:",
				allCategories: "所有分类",
				sort: "排序:",
				mostInstalls: "最多安装",
				newest: "最新",
				githubStars: "GitHub 星标",
				name: "名称",
				noMatchingServers: "未找到匹配的 MCP 服务器",
				noServersFound: "市场中未找到 MCP 服务器",
				installing: "安装中...",
				install: "安装",
				requiresApiKey: "需要 API 密钥",
				loadingPreview: "正在加载预览",
				waitingFor: "等待",
				clickToOpen: "点击在浏览器中打开",
				noTitle: "无标题",
				noDescription: "无描述信息",
				loadingImage: "正在加载图片",
				failedToLoadImage: "加载图片失败",
				addRemoteServer: "通过提供名称和 URL 端点添加远程 MCP 服务器。了解更多",
				here: "信息。",
				serverName: "服务器名称",
				serverUrl: "服务器 URL",
				serverNameRequired: "服务器名称为必填项",
				serverUrlRequired: "服务器 URL 为必填项"
			},
			clineRules: {
				manageRulesAndWorkflows: "管理 Cline 规则和工作流",
				clineRules: "Cline 规则",
				rules: "规则",
				workflows: "工作流",
				rulesDescription: "Cline 规则是包含 Cline 将始终遵循的指令和上下文的 Markdown 文件。它们会自动包含在每个任务中。在",
				workflowsDescription1: "工作流是可以通过在聊天中输入",
				workflowName: "/workflow-name",
				workflowsDescription2: "来触发的斜杠命令。它们可用于自动化常见任务或提供对常用提示的快速访问。在",
				docs: "文档",
				globalRules: "全局规则",
				workspaceRules: "工作区规则",
				globalWorkflows: "全局工作流",
				workspaceWorkflows: "工作区工作流"
			},
			qax: {
				enhancePrompt: {
					button: "✨",
					tooltip: "Qax 增强提示",
					enhancing: "增强中..."
				},
				memoryWorkspace: {
					memories: "记忆",
					memoriesTitle: "Qax 代码生成记忆",
					currentWorkspace: "当前工作区"
				},
				settings: {
					autocomplete: {
						title: "自动补全",
						enable: "启用自动补全",
						delay: "延迟 (毫秒)",
						maxLines: "最大行数",
						triggerCharacters: "触发字符",
						enableQaxAutocomplete: "启用 QAX 自动补全",
						provider: "提供商",
						openaiCompatible: "OpenAI 兼容",
						fim: "FIM (填充中间)",
						fimApi: "FIM API",
						openaiCompatibleApi: "OpenAI 兼容 API",
						temperature: "温度",
						timeout: "超时 (毫秒)",
						debounce: "防抖 (毫秒)",
						usePromptCache: "使用提示缓存",
						note: "注意",
						noteDescription: "QAX 自动补全使用 AI 模型提供智能代码补全。选择：\nOpenAI 兼容：适用于 OpenRouter、OpenAI、Azure OpenAI、本地模型（Ollama、LM Studio）和其他 OpenAI 兼容服务。\nFIM（填充中间）：专门的 API 格式，发送前缀和后缀上下文以获得更准确的补全。\n配置您的提供商设置以启用基于当前上下文的智能代码建议。"
					},
					general: {
						title: "设置",
						done: "完成",
						preferredLanguage: "首选语言",
						english: "英语",
						simplifiedChinese: "简体中文",
						languageDescription: "选择界面的首选语言"
					},
					tabs: {
						apiConfiguration: "API配置",
						autocomplete: "自动补全",
						qaxAutocompleteSettings: "QAX 自动补全设置",
						general: "通用",
						features: "功能",
						browser: "浏览器",
						terminal: "终端",
						debug: "调试",
						about: "关于"
					}
				},
				todo: {
					title: "待办列表",
					addTask: "添加任务",
					empty: "暂无任务",
					addTodoPrompt: "输入待办事项：",
					delete: "删除待办",
					deleteConfirm: "确定要删除这个待办事项吗？"
				},
				account: {
					title: "QAX 账户",
					done: "完成",
					loginPrompt: "请登录您的 QAX 账户以查看用户信息并使用 QAX 代码生成服务。",
					loginButton: "登录 QAX 账户",
					tokenStatus: "TOKEN 状态",
					expirationTime: "过期时间",
					tokenExpired: "Token 已过期，请重新登录",
					tokenExpiringSoon: "Token 将在 24 小时内过期",
					tokenValid: "Token 有效",
					logout: "退出登录"
				},
				telemetry: {
					helpImprove: "帮助改进 QAX 代码生成",
					experimentalFeatures: "实验性功能",
					description: "我们收集匿名使用数据以改进扩展。",
					startConversation: "开始对话以使用 QAX 代码生成。",
					closeBanner: "关闭横幅"
				},
				about: {
					questionsOrFeedback: "有问题或反馈？"
				}
			},
		},
	}

	return fallbacks[languageCode] || fallbacks["en"]
}

// 获取翻译文本
export function t(key: string, language: string = "en", params?: Record<string, string | number>): string {
	const keys = key.split(".")
	let current: any = resourceCache[language]

	// 如果当前语言资源不存在，尝试使用英文作为后备
	if (!current && language !== "en") {
		current = resourceCache["en"]
		// 如果英文资源也不存在，尝试使用内嵌的后备翻译
		if (!current) {
			current = getFallbackTranslations("en")
		}
	}

	// 如果当前语言资源不存在，使用内嵌的后备翻译
	if (!current) {
		current = getFallbackTranslations(language)
	}

	// 遍历键路径查找翻译
	for (const k of keys) {
		if (current && typeof current === "object" && k in current) {
			current = current[k]
		} else {
			// 如果找不到翻译，尝试使用英文后备
			if (language !== "en") {
				return t(key, "en", params)
			}
			// 如果英文也找不到，返回键名作为默认值
			current = key
			break
		}
	}

	// 如果找到的是字符串且有参数，则进行替换
	if (typeof current === "string" && params) {
		let result = current
		for (const [paramKey, paramValue] of Object.entries(params)) {
			result = result.replace(new RegExp(`{{${paramKey}}}`, "g"), String(paramValue))
		}
		return result
	}

	return typeof current === "string" ? current : key
}

// React Hook 用于在组件中使用翻译
export function useTranslation() {
	console.log("[i18n] useTranslation called")
	const { chatSettings } = useExtensionState()
	const currentLanguage = chatSettings?.preferredLanguage || "English"
	console.log("[i18n] Current language from chatSettings:", currentLanguage)

	// 将语言名称转换为代码
	const languageCode = getLanguageCode(currentLanguage)
	console.log("[i18n] Language code:", languageCode)
	const [isLoaded, setIsLoaded] = React.useState(!!resourceCache[languageCode])
	console.log("[i18n] Initial isLoaded state:", isLoaded)

	// 确保语言资源已加载
	React.useEffect(() => {
		let cancelled = false

		const loadResources = async () => {
			try {
				// 首先确保英文资源已加载（作为后备）
				if (!resourceCache["en"]) {
					await loadLanguageResources("en")
				}

				// 然后加载目标语言资源
				if (languageCode !== "en") {
					await loadLanguageResources(languageCode)
				}

				if (!cancelled) {
					setIsLoaded(true)
				}
			} catch (error) {
				console.error(`Failed to load language resources for ${languageCode}:`, error)
				if (!cancelled) {
					// 即使加载失败，也设置为已加载，因为我们有内嵌的后备翻译
					setIsLoaded(true)
				}
			}
		}

		// 重置加载状态
		setIsLoaded(false)
		loadResources()

		return () => {
			cancelled = true
		}
	}, [languageCode])

	const translate = React.useCallback(
		(key: string, params?: Record<string, string | number>) => {
			return t(key, languageCode, params)
		},
		[languageCode],
	)

	return {
		t: translate,
		language: languageCode,
		isLoaded,
	}
}

// 将语言名称转换为代码
export function getLanguageCode(languageName: string): string {
	const languageMap: Record<string, string> = {
		English: "en",
		"Arabic - العربية": "ar",
		"Portuguese - Português (Brasil)": "pt-br",
		"Czech - Čeština": "cs",
		"French - Français": "fr",
		"German - Deutsch": "de",
		"Hindi - हिन्दी": "hi",
		"Hungarian - Magyar": "hu",
		"Italian - Italiano": "it",
		"Japanese - 日本語": "ja",
		"Korean - 한국어": "ko",
		"Polish - Polski": "pl",
		"Portuguese - Português (Portugal)": "pt",
		"Russian - Русский": "ru",
		"Simplified Chinese - 简体中文": "zh-cn",
		"Spanish - Español": "es",
		"Traditional Chinese - 繁體中文": "zh-tw",
		"Turkish - Türkçe": "tr",
	}

	return languageMap[languageName] || "en"
}

// 将语言代码转换为名称
export function getLanguageName(languageCode: string): string {
	const nameMap: Record<string, string> = {
		en: "English",
		ar: "Arabic - العربية",
		"pt-br": "Portuguese - Português (Brasil)",
		cs: "Czech - Čeština",
		fr: "French - Français",
		de: "German - Deutsch",
		hi: "Hindi - हिन्दी",
		hu: "Hungarian - Magyar",
		it: "Italian - Italiano",
		ja: "Japanese - 日本語",
		ko: "Korean - 한국어",
		pl: "Polish - Polski",
		pt: "Portuguese - Português (Portugal)",
		ru: "Russian - Русский",
		"zh-cn": "Simplified Chinese - 简体中文",
		es: "Spanish - Español",
		"zh-tw": "Traditional Chinese - 繁體中文",
		tr: "Turkish - Türkçe",
	}

	return nameMap[languageCode] || "English"
}
