import { render, screen } from "@testing-library/react"
import { vi } from "vitest"
import WelcomeView from "./WelcomeView"
import { ExtensionStateContextProvider } from "@/context/ExtensionStateContext"
import { ClineAuthProvider } from "@/context/ClineAuthContext"

// Mock the grpc-client
vi.mock("@/services/grpc-client", () => ({
	AccountServiceClient: {
		accountLoginClicked: vi.fn().mockResolvedValue({}),
	},
	StateServiceClient: {
		setWelcomeViewCompleted: vi.fn().mockResolvedValue({}),
	},
}))

// Mock the i18n utils
vi.mock("@/utils/i18n", () => ({
	useTranslation: () => ({
		t: (key: string) => key, // Return the key as the translation for testing
		isLoaded: true,
	}),
}))

// Mock the validate utils
vi.mock("@/utils/validate", () => ({
	validateApiConfiguration: vi.fn().mockReturnValue(undefined),
}))

// Mock the ApiOptions component
vi.mock("@/components/settings/ApiOptions", () => ({
	default: () => <div data-testid="api-options">API Options</div>,
}))

// Mock the ClineLogoWhite component
vi.mock("@/assets/ClineLogoWhite", () => ({
	default: () => <div data-testid="cline-logo">Cline Logo</div>,
}))

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
	<ExtensionStateContextProvider>
		<ClineAuthProvider>
			{children}
		</ClineAuthProvider>
	</ExtensionStateContextProvider>
)

describe("WelcomeView", () => {
	it("renders without crashing", () => {
		render(
			<TestWrapper>
				<WelcomeView />
			</TestWrapper>
		)
		
		// Check if the welcome title is rendered
		expect(screen.getByText("welcome.title")).toBeInTheDocument()
	})

	it("shows loading state when translations are not loaded", () => {
		// Mock useTranslation to return isLoaded: false
		vi.doMock("@/utils/i18n", () => ({
			useTranslation: () => ({
				t: (key: string) => key,
				isLoaded: false,
			}),
		}))

		render(
			<TestWrapper>
				<WelcomeView />
			</TestWrapper>
		)

		expect(screen.getByText("Loading...")).toBeInTheDocument()
	})
})
