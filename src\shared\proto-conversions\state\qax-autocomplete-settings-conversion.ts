import { QaxAutocompleteSettings } from "@shared/QaxAutocompleteSettings"
import {
	AutocompleteSettings as ProtoAutocompleteSettings,
	FimSettings as ProtoFimSettings,
	AutocompleteFilterSettings as ProtoAutocompleteFilterSettings,
} from "@shared/proto/cline/state"

/**
 * Converts domain QaxAutocompleteSettings objects to proto AutocompleteSettings objects
 */
export function convertQaxAutocompleteSettingsToProtoAutocompleteSettings(
	settings: QaxAutocompleteSettings,
): ProtoAutocompleteSettings {
	return ProtoAutocompleteSettings.create({
		enabled: settings.enabled,
		provider: settings.provider,
		apiKey: settings.apiKey,
		apiBaseUrl: settings.apiBaseUrl,
		modelId: settings.modelId,
		maxTokens: settings.maxTokens,
		temperature: settings.temperature,
		requestTimeoutMs: settings.requestTimeoutMs ? Number(settings.requestTimeoutMs) : undefined,
		usePromptCache: settings.usePromptCache,
		customHeaders: settings.customHeaders ? JSON.stringify(settings.customHeaders) : undefined,
		debounceMs: settings.debounceMs,
		filter: settings.filter
			? ProtoAutocompleteFilterSettings.create({
					enabled: settings.filter.enabled || false,
					filterWhitespaceOnly: settings.filter.filterWhitespaceOnly || false,
					filterRepeatedContent: settings.filter.filterRepeatedContent || false,
					filterStrings: settings.filter.filterStrings || [],
				})
			: undefined,
		fim: settings.fim
			? ProtoFimSettings.create({
					apiKey: settings.fim.apiKey,
					baseUrl: settings.fim.baseUrl,
					modelId: settings.fim.modelId,
				})
			: undefined,
	})
}

/**
 * Converts proto AutocompleteSettings objects to domain QaxAutocompleteSettings objects
 */
export function convertProtoAutocompleteSettingsToQaxAutocompleteSettings(
	protoSettings: ProtoAutocompleteSettings,
): QaxAutocompleteSettings {
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	const settings: QaxAutocompleteSettings = {
		enabled: protoSettings.enabled,
		provider: protoSettings.provider as "openai" | "fim" | undefined,
		apiKey: protoSettings.apiKey,
		apiBaseUrl: protoSettings.apiBaseUrl,
		modelId: protoSettings.modelId,
		maxTokens: protoSettings.maxTokens,
		temperature: protoSettings.temperature,
		requestTimeoutMs: protoSettings.requestTimeoutMs ? Number(protoSettings.requestTimeoutMs) : undefined,
		usePromptCache: protoSettings.usePromptCache,
		debounceMs: protoSettings.debounceMs,
		filter: protoSettings.filter
			? {
					enabled: protoSettings.filter.enabled,
					filterWhitespaceOnly: protoSettings.filter.filterWhitespaceOnly,
					filterRepeatedContent: protoSettings.filter.filterRepeatedContent,
					filterStrings: protoSettings.filter.filterStrings || [],
				}
			: undefined,
		fim: protoSettings.fim
			? {
					apiKey: protoSettings.fim.apiKey,
					baseUrl: protoSettings.fim.baseUrl,
					modelId: protoSettings.fim.modelId,
				}
			: undefined,
	}

	// Handle custom headers JSON parsing
	try {
		if (protoSettings.customHeaders) {
			settings.customHeaders = JSON.parse(protoSettings.customHeaders)
		}
	} catch (error) {
		console.error("Failed to parse custom headers in autocomplete settings:", error)
		settings.customHeaders = {}
	}

	return settings
}
