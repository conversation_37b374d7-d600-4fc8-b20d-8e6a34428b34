import { VSCodeButton, VSCodeLink } from "@vscode/webview-ui-toolkit/react"
import { useEffect, useState, memo } from "react"
import { useExtensionState } from "@/context/ExtensionStateContext"
import { useQaxAuth } from "@/context/ClineAuthContext"
import { validateApiConfiguration } from "@/utils/validate"
import ApiOptions from "@components/settings/ApiOptions"
import { QaxAccountServiceClient, StateServiceClient } from "@/services/grpc-client"
import { EmptyRequest, BooleanRequest } from "@shared/proto/cline/common"

const QaxWelcomeView = memo(() => {
	const { apiConfiguration, chatSettings } = useExtensionState()
	const { qaxUser } = useQaxAuth()
	const [apiErrorMessage, setApiErrorMessage] = useState<string | undefined>(undefined)
	const [showApiOptions, setShowApiOptions] = useState(false)

	const disableLetsGoButton = apiErrorMessage != null

	const handleQaxLogin = () => {
		QaxAccountServiceClient.qaxLoginClicked(EmptyRequest.create()).catch((err: any) =>
			console.error("Failed to get QAX login URL:", err),
		)
	}

	const handleSubmit = async () => {
		try {
			await StateServiceClient.setWelcomeViewCompleted(BooleanRequest.create({ value: true }))
		} catch (error) {
			console.error("Failed to update API configuration or complete welcome view:", error)
		}
	}

	useEffect(() => {
		setApiErrorMessage(validateApiConfiguration(chatSettings.mode, apiConfiguration))
	}, [apiConfiguration, chatSettings.mode])

	return (
		<div className="fixed inset-0 p-0 flex flex-col">
			<div className="h-full px-5 overflow-auto">
				<h2>欢迎使用 QAX Codegen</h2>
				<div className="flex justify-center my-5">
					<div className="size-16 rounded-full bg-[var(--vscode-button-background)] flex items-center justify-center text-2xl text-[var(--vscode-button-foreground)]">
						QAX
					</div>
				</div>
				<p>QAX Codegen 是奇安信推出的智能代码生成助手，基于先进的大语言模型技术，能够帮助您：</p>
				<ul className="text-[var(--vscode-foreground)] ml-4 mb-4">
					<li>• 自动生成高质量代码</li>
					<li>• 进行代码审查和安全分析</li>
					<li>• 提供编程最佳实践建议</li>
					<li>• 协助解决复杂的技术问题</li>
				</ul>

				<p className="text-[var(--vscode-descriptionForeground)]">
					{qaxUser ? (
						<span>
							欢迎回来，{qaxUser.displayName || qaxUser.name || qaxUser.email}！ 您已成功登录 QAX 账户，可以开始使用
							QAX Codegen 服务。
						</span>
					) : (
						<span>请登录您的 QAX 账户以开始使用 QAX Codegen 服务，或使用自定义 API 密钥。</span>
					)}
				</p>

				{!qaxUser ? (
					<VSCodeButton appearance="primary" onClick={handleQaxLogin} className="w-full mt-1">
						登录 QAX 账户
					</VSCodeButton>
				) : (
					<VSCodeButton appearance="primary" onClick={handleSubmit} className="w-full mt-1">
						开始使用
					</VSCodeButton>
				)}

				{!showApiOptions && (
					<VSCodeButton
						appearance="secondary"
						onClick={() => setShowApiOptions(!showApiOptions)}
						className="mt-2.5 w-full">
						使用自定义 API 密钥
					</VSCodeButton>
				)}

				<div className="mt-4.5">
					{showApiOptions && (
						<div>
							<ApiOptions showModelOptions={false} currentMode={chatSettings.mode} />
							<VSCodeButton onClick={handleSubmit} disabled={disableLetsGoButton} className="mt-0.75">
								开始使用！
							</VSCodeButton>
						</div>
					)}
				</div>

				<div className="mt-6 pt-4 border-t border-[var(--vscode-widget-border)]">
					<p className="text-[var(--vscode-descriptionForeground)] text-xs text-center m-0">
						QAX Codegen 由奇安信集团提供技术支持 |{" "}
						<VSCodeLink href="https://www.qianxin.com" className="inline">
							了解更多
						</VSCodeLink>
					</p>
				</div>
			</div>
		</div>
	)
})

export default QaxWelcomeView
